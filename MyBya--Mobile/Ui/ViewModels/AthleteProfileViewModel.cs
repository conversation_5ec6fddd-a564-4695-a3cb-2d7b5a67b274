using System;
using System.ComponentModel;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using MyBya.Constants;
using MyBya.Enums;
using MyBya.Helpers;
using MyBya.Interfaces;
using MyBya.Models;
using MyBya.Services.DTOs;
using MyBya.Shared;
using MyBya.Ui.Pages;
using MyBya.Ui.ViewModels.Common;
using MyBya.Ui.Views;
using MyBya.Ui.Views.Popups;
using Serilog;

namespace MyBya.Ui.ViewModels;

public class AthleteProfileViewModel : ViewModelBase
{
    private readonly IAuthenticationService _authenticationService;

    public IAsyncRelayCommand SaveCommand { get; }
    public ICommand PasswordResetCommand { get; set; }
    public IAsyncRelayCommand DeleteAccountCommand { get; set; }

    private string firstName;
    public string FirstName
    {
        get => firstName;
        set => SetProperty(ref firstName, value);
    }

    private string lastName;
    public string LastName
    {
        get => lastName;
        set => SetProperty(ref lastName, value);
    }

    private string email;
    public string Email
    {
        get => email;
        set => SetProperty(ref email, value);
    }

    private string password;
    public string Password
    {
        get => password;
        set => SetProperty(ref password, value);
    }

    private string confirmPassword;
    public string ConfirmPassword
    {
        get => confirmPassword;
        set => SetProperty(ref confirmPassword, value);
    }

    private string selectedDate;
    public string SelectedDate
    {
        get => selectedDate;
        set => SetProperty(ref selectedDate, value);
    }

    private SportEnum? selectedSport;
    public SportEnum? SelectedSport
    {
        get => selectedSport;
        set => SetProperty(ref selectedSport, value);
    }

    private LevelEnum? selectedLevel;
    public LevelEnum? SelectedLevel
    {
        get => selectedLevel;
        set => SetProperty(ref selectedLevel, value);
    }

    private bool isFirstTimeSetup;
    public bool IsFirstTimeSetup
    {
        get => isFirstTimeSetup;
        set => SetProperty(ref isFirstTimeSetup, value);
    }

    private bool showMyAccountSettings;
    public bool ShowMyAccountSettings
    {
        get => showMyAccountSettings;
        set => SetProperty(ref showMyAccountSettings, value);
    }

    public AthleteProfileViewModel()
    {
        _authenticationService = ServiceHelper.GetService<IAuthenticationService>();
        SaveCommand = new AsyncRelayCommand(SaveAsync);
        IsFirstTimeSetup = false; // Default value, can be set externally
        PasswordResetCommand = new Command(async () => await PasswordReset());
        DeleteAccountCommand = new AsyncRelayCommand(DeleteAccountAsync);
    }

    private async Task DeleteAccountAsync()
    {
        try
        {
            bool confirm = await ShowConfirmationPopup(
                "Delete Account",
                "Are you sure you want to delete your account? This action cannot be undone.");

            if (!confirm)
                return;

            IsBusy = true;

            var userId = MyByaContext.Instance.CurrentUser?.Id;

            if (string.IsNullOrEmpty(userId))
            {
                Log.Logger.Error("User ID is null or empty during account deletion.");
                return;
            }

            var result = await _authenticationService.DeleteUserAsync(userId);

            if (result)
            {
                await ShowToastMessage("Account deleted successfully.");
                await _authenticationService.LogoutAsync();

                if (Application.Current != null)
                {
                    Application.Current.MainPage = new NavigationPage(
                        new LoginPage(new LoginViewModel())
                    );
                }
            }
            else
            {
                await ShowToastMessage("Failed to delete account. Please try again.");
            }
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Error deleting user account");
            await ShowToastMessage("An error occurred while deleting the account. Please try again.");
        }
        finally
        {
            IsBusy = false;
        }
    }

    private async Task PasswordReset()
    {
        bool confirm = await ShowConfirmationPopup(
            "Password Reset",
            "Are you sure you would like to reset your password?");
    }

    private bool ArePasswordsMatching()
    {
        if (string.IsNullOrEmpty(Password) || string.IsNullOrEmpty(ConfirmPassword))
            return false;

        if (!IsPasswordValid(Password))
            return false;

        return Password == ConfirmPassword;
    }

    private bool IsPasswordValid(string password)
    {
        if (password.Length < 6)
            return false;

        bool hasNumber = password.Any(char.IsDigit);
        bool hasUpperCase = password.Any(char.IsUpper);
        bool hasLowerCase = password.Any(char.IsLower);
        bool hasNonAlphanumeric = password.Any(ch => !char.IsLetterOrDigit(ch));

        return hasNumber && hasUpperCase && hasLowerCase && hasNonAlphanumeric;
    }

    private async Task SaveAsync()
    {
        try
        {
            if (IsFirstTimeSetup)
            {
                if (!ArePasswordsMatching())
                {
                    if (!IsPasswordValid(Password))
                    {
                        await _alertService.ShowAlertAsync("MyBya", "Password must be at least 6 characters long and contain at least one digit, one uppercase letter, one lowercase letter, and one special character.");
                        return;
                    }

                    await ShowToastMessage("Passwords do not match!");
                    return;
                }
            }

            if (string.IsNullOrEmpty(Email) ||
                string.IsNullOrEmpty(FirstName) ||
                string.IsNullOrEmpty(LastName) ||
                SelectedSport == null || SelectedSport == SportEnum.NONE ||
                SelectedLevel == null || SelectedLevel == LevelEnum.NONE)
            {
                await ShowToastMessage("Please fill in all required fields.");
                return;
            }

            IsBusy = true;

            if (IsFirstTimeSetup)
            {
                await CreateUserAsync();
                return;
            }

            await UpdateUserAsync();
        }
        catch (Exception ex)
        {
            var operation = IsFirstTimeSetup ? "creating" : "updating";
            Log.Logger.Error(ex, $"Error {operation} user");
            await ShowToastMessage($"An error occurred while {operation} the user.");
        }
        finally
        {
            IsBusy = false;
        }
    }

    private async Task UpdateUserAsync()
    {
        var editUserDto = new EditUserDto
        {
            Id = MyByaContext.Instance.CurrentUser?.Id ?? string.Empty,
            UserName = Email,
            Email = Email,
            Name = FirstName,
            Surname = LastName,
            LockoutEnabled = true,
            IsActive = true,
            ExtraProperties = new Dictionary<string, object>
            {
                ["primary_sport"] = (int)SelectedSport,
                ["level"] = (int)SelectedLevel
            }
        };

        var abpUser = await _authenticationService.UpdateUserAsync(editUserDto);

        if (abpUser != null)
        {
            await ShowToastMessage("User updated successfully!");
            MyByaContext.Instance.SetCurrentUser(abpUser);
            return;
        }

        await ShowToastMessage("Failed to update user. Please try again.");
    }

    private async Task CreateUserAsync()
    {
        var createUserDto = new CreateUserDto
        {
            UserName = Email,
            Email = Email,
            Password = Password,
            Name = FirstName,
            Surname = LastName,
            LockoutEnabled = true,
            IsActive = true,
            ExtraProperties = new Dictionary<string, object>
            {
                ["primarySport"] = (int)SelectedSport,
                ["level"] = (int)SelectedLevel
            }
        };

        var result = await _authenticationService.CreateUserAsync(createUserDto);

        if (result != null)
        {
            await ShowToastMessage("User created successfully!");

            var loginResult = await _authenticationService
                .LoginAsync(Email.Trim(), Password.Trim());

            if (loginResult == null)
            {
                await ShowMessageError();
                return;
            }

            if (loginResult.Success)
            {
                await SecureStorage.Default.SetAsync(SecureStorageConstants.UserEmail, Email);

                if (Application.Current != null)
                    Application.Current.MainPage = new NavigationPage(new MainTabbedPage());
            }
            else
            {
                await ShowMessageError("Login failed. Please check your credentials.");
            }

            return;
        }

        await ShowToastMessage("Failed to create user. Please try again.");
    }

    public override async Task OnAppearing()
    {
        await base.OnAppearing();

        if (!IsFirstTimeSetup)
        {
            Email = MyByaContext.Instance.CurrentUser?.Email ?? string.Empty;
            FirstName = MyByaContext.Instance.CurrentUser?.Name ?? string.Empty;
            LastName = MyByaContext.Instance.CurrentUser?.Surname ?? string.Empty;
            SelectedSport = GetSportEnum();
            SelectedLevel = GetLevelEnum();
        }
    }

    public SportEnum GetSportEnum()
    {
        if (MyByaContext.Instance.CurrentUser?.ExtraProperties?["primary_sport"] is long sportValue)
        {
            return (SportEnum)sportValue;
        }

        return SportEnum.NONE;
    }

    public LevelEnum GetLevelEnum()
    {
        if (MyByaContext.Instance.CurrentUser?.ExtraProperties?["level"] is long levelValue)
        {
            return (LevelEnum)levelValue;
        }

        return LevelEnum.NONE;
    }

    public override Task OnNavigatingTo(object? parameter)
    {
        if (parameter is bool showMyAccountSettings)
        {
            ShowMyAccountSettings = showMyAccountSettings;
        }

        return base.OnNavigatingTo(parameter);
    }
}
