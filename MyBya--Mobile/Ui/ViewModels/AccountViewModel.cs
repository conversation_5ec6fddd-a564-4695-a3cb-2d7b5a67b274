using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using MyBya.Helpers;
using MyBya.Interfaces;
using MyBya.Models;
using MyBya.Services.DTOs;
using MyBya.Services.Terra;
using MyBya.Shared;
using MyBya.Ui.Pages;
using MyBya.Ui.ViewModels.Common;
using Serilog;

namespace MyBya.Ui.ViewModels;

public class AccountViewModel : ViewModelBase
{
    private string _userName;
    public string UserName
    {
        get => _userName;
        set => SetProperty(ref _userName, value);
    }

    private string _userEmail;
    public string UserEmail
    {
        get => _userEmail;
        set => SetProperty(ref _userEmail, value);
    }

    private string _appVersion;
    public string AppVersion
    {
        get => _appVersion;
        set => SetProperty(ref _appVersion, value);
    }

    public ICommand EditProfileCommand { get; }
    public IAsyncRelayCommand MyTrainingPlansCommand { get; }
    public IAsyncRelayCommand MyTestsCommand { get; }
    public ICommand PrivacyPolicyCommand { get; }
    public ICommand TermsOfServiceCommand { get; }
    public ICommand AboutCommand { get; }
    public ICommand LogoutCommand { get; }
    public IAsyncRelayCommand TerraAuthCommand { get; set; }
    public IRelayCommand WorkoutHistoryCommand { get; set; }

    public AccountViewModel()
    {
        EditProfileCommand = new Command(OnEditProfile);
        MyTrainingPlansCommand = new AsyncRelayCommand(OnMyTrainingPlans);
        MyTestsCommand = new AsyncRelayCommand(OnMyTests);
        PrivacyPolicyCommand = new Command(OnPrivacyPolicy);
        TermsOfServiceCommand = new Command(OnTermsOfService);
        AboutCommand = new Command(OnAbout);
        LogoutCommand = new Command(OnLogout);
        TerraAuthCommand = new AsyncRelayCommand(TerraAuthorization);
        WorkoutHistoryCommand = new RelayCommand(OnWorkoutHistory);

        LoadUserInfo();
    }

    private void OnWorkoutHistory()
    {
       var currentTabbedPage = NavigationHelper.GetCurrentTabbedPage();

        if (currentTabbedPage != null &&
            currentTabbedPage.Children != null &&
            currentTabbedPage.Children.Count > 3)
        {
            currentTabbedPage.CurrentPage = currentTabbedPage.Children[3];
        }
    }

    public override async Task OnAppearing()
    {
        await base.OnAppearing();
        LoadUserInfo();
    }

    private void LoadUserInfo()
    {
        UserName = MyByaContext.Instance.GetUserName() ?? "";
        UserEmail = MyByaContext.Instance.GetUserEmail() ?? "";
        AppVersion = AppInfo.VersionString;
    }

    private async void OnEditProfile()
    {
        if (Application.Current?.MainPage?.Navigation != null)
        {
            await Application.Current.MainPage.Navigation.PushAsync(new AthleteProfilePage(new AthleteProfileViewModel
            {
                ShowMyAccountSettings = true
            }));
        }
    }

    private async Task OnMyTrainingPlans()
    {
        await _navigationService.NavigateToPage<MyTrainingPlansPage>();
    }

    private async Task OnMyTests()
    {
        await _navigationService.NavigateToPage<MyTestsPage>();
    }

    private async void OnPrivacyPolicy()
    {
        await ShowToastMessage("Privacy Policy feature coming soon!");
    }

    private async void OnTermsOfService()
    {
        await ShowToastMessage("Terms of Service feature coming soon!");
    }

    private async void OnAbout()
    {
        await _alertService.ShowAlertAsync("About MyBya",
            $"MyBya Mobile App\nVersion: {AppVersion}\n\nYour fitness companion for test protocols and training.");
    }

    private async void OnLogout()
    {
        try
        {
            bool confirm = await ShowConfirmationPopup("Logout", "Are you sure you wish to logout of MyBya?");

            if (!confirm)
                return;

            var result = await ServiceHelper
                .GetService<IAuthenticationService>()
                .LogoutAsync();

            if (result)
            {
                if (Application.Current != null)
                    Application.Current.MainPage = new NavigationPage(
                        new LoginPage(new LoginViewModel())
                    );
            }
            else
            {
                await ShowToastMessage("Logout failed. Please try again.");
            }
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Error during logout", ex.Message);

            await _alertService.ShowAlertAsync(
                "Logout Error",
                "An error occurred while trying to log out. Please try again later."
            );
        }
    }

    private async Task TerraAuthorization()
    {
        await _navigationService.NavigateToPage<TerraAuthWebViewPage>();
    }
}
