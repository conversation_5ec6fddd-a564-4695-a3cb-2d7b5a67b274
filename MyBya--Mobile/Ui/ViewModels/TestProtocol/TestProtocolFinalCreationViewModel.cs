using System.ComponentModel;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using MyBya.Enums;
using MyBya.Helpers;
using MyBya.Models;
using MyBya.Parameters;
using MyBya.Services;
using MyBya.Services.DTOs;
using MyBya.Shared;
using MyBya.Ui.Controls;
using MyBya.Ui.Pages.TestProtocol;
using MyBya.Ui.ViewModels.Common;
using Serilog;

namespace MyBya.Ui.ViewModels.TestProtocol;

public class TestProtocolFinalCreationViewModel : ViewModelBase
{
    private TestProtocolCreationParameters? testProtocolCreationParameters { get; set; }

    private SportEnum _selectedSport;
    private LevelEnum _selectedLevel;
    private string _selectedEvent;
    private int eventId;

    public SportEnum SelectedSport
    {
        get => _selectedSport;
        set => SetProperty(ref _selectedSport, value);
    }

    public LevelEnum SelectedLevel
    {
        get => _selectedLevel;
        set => SetProperty(ref _selectedLevel, value);
    }

    public string SelectedEvent
    {
        get => _selectedEvent;
        set => SetProperty(ref _selectedEvent, value);
    }

    private bool isFinishButtonEnabled;
    public bool IsFinishButtonEnabled
    {
        get => isFinishButtonEnabled;
        set => SetProperty(ref isFinishButtonEnabled, value);
    }

    private int? minutes;
    public int? Minutes
    {
        get => minutes;
        set => SetProperty(ref minutes, value);
    }

    private int? seconds;
    public int? Seconds
    {
        get => seconds;
        set => SetProperty(ref seconds, value);
    }

    private bool isTreadmillSelected;
    public bool IsTreadmillSelected
    {
        get => isTreadmillSelected;
        set => SetProperty(ref isTreadmillSelected, value);
    }

    private bool isTrackSelected;
    public bool IsTrackSelected
    {
        get => isTrackSelected;
        set => SetProperty(ref isTrackSelected, value);
    }

    private bool isHeartRateAndLactateSelected;
    public bool IsHeartRateAndLactateSelected
    {
        get => isHeartRateAndLactateSelected;
        set => SetProperty(ref isHeartRateAndLactateSelected, value);
    }

    private bool isHeartRateOnlySelected;
    public bool IsHeartRateOnlySelected
    {
        get => isHeartRateOnlySelected;
        set => SetProperty(ref isHeartRateOnlySelected, value);
    }

    public IAsyncRelayCommand FinishButtonCommand { get; set; }
    public IAsyncRelayCommand BackButtonCommand { get; set; }

    public TestProtocolFinalCreationViewModel()
    {
        _selectedEvent = string.Empty;
        IsFinishButtonEnabled = false;
        FinishButtonCommand = new AsyncRelayCommand(Finish);
        BackButtonCommand = new AsyncRelayCommand(Back);
    }

    private async Task Back()
    {
        await _navigationService.NavigateBack();
    }

    private async Task Finish()
    {
        try
        {
            IsBusy = true;

            int minutes = Minutes ?? 0;
            int seconds = Seconds ?? 0;

            var formattedTime = $"{minutes:D2}:{seconds:D2}";

            var entity = new AthleteTestSetupDto
            {
                DataType = IsHeartRateOnlySelected ? 0 : 1,
                UserId = MyByaContext.Instance.GetUserId(),
                Sport = SelectedSport,
                Time = formattedTime,
                IntervalType = IsTreadmillSelected
                    ? IntervalTypeEnum.TimeBased
                    : IntervalTypeEnum.DistanceBased,
                Level = SelectedLevel,
                TrainingPlanEventId = eventId,
            };

            AthleteTestSetupModel? model = await ServiceHelper
                .GetService<AthleteTestSetupService>()
                .CreateAthleteTest(entity);

            if (model == null)
            {
                await ShowMessageError();
                return;
            }

            if (model != null)
            {
                MyByaContext.Instance.SetCurrentTestSetup(model);

                var testCalendarService = ServiceHelper.GetService<TestCalendarService>();

                if (testCalendarService == null ||
                   !model.TestCalendarId.HasValue)
                {
                    return;
                }

                TestCalendarModel? testCalendar = await testCalendarService
                    .GetTestCalendar(model.TestCalendarId.Value);

                if (testCalendar == null)
                {
                    return;
                }

                MyByaContext.Instance.SetCurrentTestCalendar(testCalendar);

                var parameters = new TestProtocolParameters
                {
                    Sport = SelectedSport,
                    IntervalType = entity.IntervalType,
                    IntervalDistance = testCalendar.StandardInterval.HasValue
                        ? testCalendar.StandardInterval.Value
                        : 0,
                    Level = SelectedLevel,
                    Event = SelectedEvent,
                    Time = formattedTime,
                    IsTreadmill = IsTreadmillSelected,
                    IsTrack = IsTrackSelected,
                    IsHeartRateAndLactate = IsHeartRateAndLactateSelected,
                    IsHeartRateOnly = IsHeartRateOnlySelected
                };

                await _navigationService.NavigateToPage<YourTestProtocolPage>(parameters);
            }
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            await ShowMessageError();
        }
        finally
        {
            IsBusy = false;
        }
    }

    public override Task OnNavigatingTo(object? parameter)
    {
        if (parameter is TestProtocolCreationParameters parameters)
        {
            testProtocolCreationParameters = parameters;
            SelectedSport = parameters.Sport;
            SelectedLevel = parameters.Level;
            SelectedEvent = parameters.Event;
            eventId = parameters.EventId;
        }

        return base.OnNavigatingTo(parameter);
    }

    protected override void ViewModelBase_PropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        try
        {
            base.ViewModelBase_PropertyChanged(sender, e);

            if (e.PropertyName == nameof(IsTreadmillSelected) ||
                e.PropertyName == nameof(IsTrackSelected) ||
                e.PropertyName == nameof(IsHeartRateAndLactateSelected) ||
                e.PropertyName == nameof(IsHeartRateOnlySelected) ||
                e.PropertyName == nameof(Minutes) || e.PropertyName == nameof(Seconds))
            {
                IsFinishButtonEnabled = (Minutes > 0 || Seconds > 0) &&
                                        (IsTreadmillSelected || IsTrackSelected) &&
                                        (IsHeartRateAndLactateSelected || IsHeartRateOnlySelected);
            }
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            throw;
        }
    }
}
