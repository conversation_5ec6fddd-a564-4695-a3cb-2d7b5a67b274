using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using MyBya.Enums;
using MyBya.Helpers;
using MyBya.Models;
using MyBya.Services;
using MyBya.Services.DTOs;
using MyBya.Shared;
using MyBya.Ui.Controls;
using MyBya.Ui.Pages.TestProtocol;
using MyBya.Ui.ViewModels.Common;
using Serilog;

namespace MyBya.Ui.ViewModels.TestProtocol;

public class TestProtocolSetupViewModel : ViewModelBase
{
    private List<string> athletes;
    public List<string> Athletes
    {
        get => athletes;
        set => SetProperty(ref athletes, value);
    }

    private List<SportEnum> sports;
    public List<SportEnum> Sports
    {
        get => sports;
        set => SetProperty(ref sports, value);
    }

    private List<RunningModeEnum> trackOrTreadmillOptions;
    public List<RunningModeEnum> TrackOrTreadmillOptions
    {
        get => trackOrTreadmillOptions;
        set => SetProperty(ref trackOrTreadmillOptions, value);
    }

    private List<LevelEnum> levels;
    public List<LevelEnum> Levels
    {
        get => levels;
        set => SetProperty(ref levels, value);
    }

    private string runPace;
    public string RunPace
    {
        get => runPace;
        set => SetProperty(ref runPace, value);
    }

    private string selectedAthlete;
    public string SelectedAthlete
    {
        get => selectedAthlete;
        set => SetProperty(ref selectedAthlete, value);
    }

    private SportEnum selectedSport;
    public SportEnum SelectedSport
    {
        get => selectedSport;
        set => SetProperty(ref selectedSport, value);
    }

    private RunningModeEnum selectedTrackOrTreadmill;
    public RunningModeEnum SelectedTrackOrTreadmill
    {
        get => selectedTrackOrTreadmill;
        set => SetProperty(ref selectedTrackOrTreadmill, value);
    }

    private LevelEnum selectedLevel;
    public LevelEnum SelectedLevel
    {
        get => selectedLevel;
        set => SetProperty(ref selectedLevel, value);
    }

    private List<AthleteModel> _athletes {get; set;}

    public IAsyncRelayCommand ContinueCommand{get; set;}

    public TestProtocolSetupViewModel()
    {
        athletes = new List<string>();
        sports = new List<SportEnum>();
        trackOrTreadmillOptions = new List<RunningModeEnum>();
        levels = new List<LevelEnum>();
        runPace = string.Empty;
        selectedAthlete = string.Empty;
        selectedSport = default;
        selectedTrackOrTreadmill = default;
        selectedLevel = default;
        _athletes = new List<AthleteModel>();

        Athletes = new List<string>();

        TrackOrTreadmillOptions = new List<RunningModeEnum>()
        {
            RunningModeEnum.TRACK,
            RunningModeEnum.TREADMILL
        };

        Sports = new List<SportEnum>
        {
            SportEnum.RUNNING,
            //SportEnum.ROWING,
            //SportEnum.CYCLING,
            //SportEnum.SWIMMING
        };

        Levels = new List<LevelEnum>
        {
            LevelEnum.BEGINNER,
            LevelEnum.INTERMEDIATE,
            LevelEnum.ADVANCED,
        };

        ContinueCommand = new AsyncRelayCommand(Continue);
    }

    private async Task Continue()
    {
        IsBusy = true;

        try
        {
            int memberId = GetMemberId();

        var athleteTestSetupDto = new AthleteTestSetupDto
        {
            UserId = memberId,
            Level = SelectedLevel,
            Sport = SelectedSport,
            IntervalType = SelectedTrackOrTreadmill == RunningModeEnum.TRACK
                ? IntervalTypeEnum.DistanceBased
                : IntervalTypeEnum.TimeBased,
            Time = RunPace
        };

        AthleteTestSetupModel? model = await ServiceHelper
            .GetService<AthleteTestSetupService>()
            .CreateAthleteTest(athleteTestSetupDto);

            if (model != null)
            {
                MyByaContext.Instance.SetCurrentTestSetup(model);
                await _navigationService.NavigateToPage<EnterTestDataPage>(model);
            }
        }
        finally
        {
            IsBusy = false;
        }
    }

    private int GetMemberId()
    {
        if (_athletes is null)
            return 0;

        AthleteModel? athlete = _athletes?
            .FirstOrDefault(x => x.FullName == SelectedAthlete);

        return athlete is null ? 0 : athlete.MemberId;
    }

    public override async Task OnAppearing()
    {
        try
        {
            await base.OnAppearing();

            IsBusy = true;

            _athletes = await ServiceHelper
                .GetService<AthletesService>()
                .GetAthletes();

            if (_athletes is null || !_athletes.Any())
                return;

            var athleteTest = _athletes.FirstOrDefault();

            var names = new List<string>();

            if (athleteTest?.FullName != null)
                names.Add(athleteTest.FullName);

            foreach (AthleteModel athlete in _athletes)
            {
                if (athlete.FullName != null)
                    names.Add(athlete.FullName);
            }

            Athletes = names;
        }
        catch (Exception ex)
        {
            await ShowMessageError();
            Log.Error(ex.Message, ex);
        }
        finally
        {
            IsBusy = false;
        }
    }
}
