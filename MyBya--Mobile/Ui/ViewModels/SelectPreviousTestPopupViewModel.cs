using System;
using MyBya.Enums;
using MyBya.Helpers;
using MyBya.Models;
using MyBya.Services;
using MyBya.Shared;
using MyBya.Ui.ViewModels.Common;
using Serilog;

namespace MyBya.Ui.ViewModels;

public class SelectPreviousTestPopupViewModel : ViewModelBase
{
    private List<int> testDetailAthleteIdsToAvoid = new List<int>();
    private SportEnum CurrentSport { get; set; }

    private List<string> previousTestDates = new List<string>();
    public List<string> PreviousTestDates
    {
        get => previousTestDates;
        set => SetProperty(ref previousTestDates, value);
    }

    private bool isSelectButtonEnabled;
    public bool IsSelectButtonEnabled
    {
        get => isSelectButtonEnabled;
        set => SetProperty(ref isSelectButtonEnabled, value);
    }

    private List<TestDateModel> previousTestDatesModels = new List<TestDateModel>();

    public SelectPreviousTestPopupViewModel(SportEnum sportEnum)
    {
        CurrentSport = sportEnum;

        Task.Run(InitializeAsync)
            .ContinueWith(t =>
            {
                if (t.IsFaulted)
                    Log.Logger.Error(t.Exception, "Error initializing SelectPreviousTestPopupViewModel");
            });
    }

    public async Task InitializeAsync()
    {
        try
        {
            IsBusy = true;

            previousTestDatesModels = await ServiceHelper
                .GetService<AthleteTestSetupService>()
                .GetTestsByMemberAndSport(MyByaContext.Instance.GetMemberId(), (int)CurrentSport);

            if (previousTestDatesModels.Count > 0)
            {
                SetFilteredDates();
            }
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Error loading previous test dates");
            await ShowMessageError();
            throw;
        }
        finally
        {
            IsBusy = false;
        }
    }

    public void SetFilteredDates()
    {
        var filteredModels = GetFilteredTestDates();

        PreviousTestDates = filteredModels
            .Select(x => x.Date.ToString("D"))
            .Where(date => !string.IsNullOrEmpty(date))
            .ToList();
    }

    public TestDateModel? GetSelectedTestByDate(string selectedDate)
    {
        if (string.IsNullOrEmpty(selectedDate))
        {
            return null;
        }

        if (!DateTime.TryParse(selectedDate, out DateTime date))
            return null;

        return previousTestDatesModels
            .FirstOrDefault(x => x.Date.Date == date.Date);
    }

    public void AddTestDetailAthleteIdsToAvoid(List<int> testDetailAthleteIdsToAvoid)
    {
        this.testDetailAthleteIdsToAvoid = testDetailAthleteIdsToAvoid;
        SetFilteredDates();
    }

    public List<TestDateModel> GetFilteredTestDates()
    {
        if (testDetailAthleteIdsToAvoid == null || !testDetailAthleteIdsToAvoid.Any())
            return previousTestDatesModels;

        return previousTestDatesModels
            .Where(x => !testDetailAthleteIdsToAvoid.Contains(x.TestDetailAthleteId))
            .ToList();
    }
}
