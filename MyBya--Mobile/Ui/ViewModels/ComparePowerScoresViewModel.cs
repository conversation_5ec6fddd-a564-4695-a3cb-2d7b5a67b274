using System;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using MyBya.Enums;
using MyBya.Helpers;
using MyBya.Models;
using MyBya.Services;
using MyBya.Shared;
using MyBya.Ui.ViewModels.Common;
using MyBya.Ui.Views;
using MyBya.Ui.Views.Popups;
using Serilog;

namespace MyBya.Ui.ViewModels;

public class ComparePowerScoresViewModel : ListViewModelBase<BepsTestResultModel>
{
    public SportEnum SelectedSport { get; set; }
    public IAsyncRelayCommand AddTestCommand { get; set; }
    public ICommand DeleteTestCommand { get; set; }
    private int positionCounter = 0;

    private ObservableCollection<BeepScoresModel> chartItemsTest1;
    public ObservableCollection<BeepScoresModel> ChartItemsTest1
    {
        get => chartItemsTest1;
        set => SetProperty(ref chartItemsTest1, value);
    }

    private ObservableCollection<BeepScoresModel> chartItemsTest2;
    public ObservableCollection<BeepScoresModel> ChartItemsTest2
    {
        get => chartItemsTest2;
        set => SetProperty(ref chartItemsTest2, value);
    }

    private ObservableCollection<BeepScoresModel> chartItemsTest3;
    public ObservableCollection<BeepScoresModel> ChartItemsTest3
    {
        get => chartItemsTest3;
        set => SetProperty(ref chartItemsTest3, value);
    }

    private ObservableCollection<BeepScoresModel> chartItemsTest4;
    public ObservableCollection<BeepScoresModel> ChartItemsTest4
    {
        get => chartItemsTest4;
        set => SetProperty(ref chartItemsTest4, value);
    }

    private bool isAddTestButtonVisible;
    public bool IsAddTestButtonVisible
    {
        get => isAddTestButtonVisible;
        set => SetProperty(ref isAddTestButtonVisible, value);
    }

    private SolidColorBrush barColorTest1;
    private SolidColorBrush barColorTest2;
    private SolidColorBrush barColorTest3;
    private SolidColorBrush barColorTest4;

    public SolidColorBrush BarColorTest1
    {
        get => barColorTest1;
        set => SetProperty(ref barColorTest1, value);
    }

    public SolidColorBrush BarColorTest2
    {
        get => barColorTest2;
        set => SetProperty(ref barColorTest2, value);
    }

    public SolidColorBrush BarColorTest3
    {
        get => barColorTest3;
        set => SetProperty(ref barColorTest3, value);
    }

    public SolidColorBrush BarColorTest4
    {
        get => barColorTest4;
        set => SetProperty(ref barColorTest4, value);
    }

    public ComparePowerScoresViewModel()
    {
        AddTestCommand = new AsyncRelayCommand(ShowPreviousTests);
        DeleteTestCommand = new Command<object>(DeleteTest);
        ChartItemsTest1 = [];
        ChartItemsTest2 = [];
        ChartItemsTest3 = [];
        ChartItemsTest4 = [];
        BarColorTest1 = new(Color.FromArgb("#FF7FE8"));
        BarColorTest2 = new(Color.FromArgb("#231B3B"));
        BarColorTest3 = new(Color.FromArgb("#72FFFF"));
        BarColorTest4 = new(Color.FromArgb("#6F658C"));
    }

    private void DeleteTest(object test)
    {
        if (test is BepsTestResultModel bepsTestResult && Items != null)
        {
            if (Items.Contains(bepsTestResult))
            {
                RemoveChartItemsByIndex(bepsTestResult.Position);
                Items.Remove(bepsTestResult);

                // Update positions of remaining items and their charts
                for (int i = 0; i < Items.Count; i++)
                {
                    int position = i + 1;

                    // Skip the first chart as it is always the most recent test
                    if (position == 1)
                        continue;

                    // Need to remove chart items for the next position as well
                    RemoveChartItemsByIndex(position + 1);
                    SetChartItemsByIndex(position, Items[i]);
                    Items[i].Position = position;
                    Items[i].SetColor(position);
                }

                positionCounter = Items.Count;
                IsAddTestButtonVisible = Items.Count < 4;
            }
        }
    }

    private async Task ShowPreviousTests()
    {
        List<int> testDetailAthleteIdsToAvoid = Items
            .Select(x => x.TestDetailAthleteId)
            .ToList();

        var popup = new SelectPreviousTestPopup(SelectedSport);
        popup.AddTestDetailAthleteIdsToAvoid(testDetailAthleteIdsToAvoid);
        popup.Callback = SelectedTestCallback;
        await ShowPopupAsync(popup);
    }

    private async Task SelectedTestCallback(TestDateModel testDate)
    {
        try
        {
            IsBusy = true;

            if (testDate == null)
            {
                await ShowMessageError("No valid test selected.");
                return;
            }

            await AddTest(testDate.TestDetailAthleteId);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Error setting selected test");
            await ShowMessageError("An error occurred while setting the selected test.");
        }
        finally
        {
            IsBusy = false;
        }
    }

    public override async Task OnAppearing()
    {
        await base.OnAppearing();

        try
        {
            IsBusy = true;

            List<AthleteTestSetupModel> athleteTestSetup = await ServiceHelper
                .GetService<AthleteTestSetupService>()
                .GetMostRecentTestsByMemberId(MyByaContext.Instance.GetMemberId());

            if (athleteTestSetup == null || athleteTestSetup.Count == 0)
            {
                await ShowMessageError("No recent tests found for the athlete.");
                return;
            }

            var mostRecentTest = athleteTestSetup
                .FirstOrDefault(x => x.Sport == (int)SelectedSport);

            if (mostRecentTest == null)
            {
                await ShowMessageError("No recent tests found for the selected sport.");
                return;
            }

            await AddTest(mostRecentTest.TestDetailAthleteId ?? 0);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            await ShowMessageError("An error occurred while loading the data.");
        }
        finally
        {
            IsBusy = false;
        }
    }

    private async Task AddTest(int testDetailAthleteId)
    {
        var bepsTestData = await ServiceHelper
            .GetService<BepsTestDataService>()
            .CreateBepsResult(testDetailAthleteId);

        if (bepsTestData != null)
        {
            positionCounter++;

            var bepsTestResult = new BepsTestResultModel
            {
                Id = bepsTestData.Id,
                TestDetailAthleteId = bepsTestData.TestDetailAthleteId,
                Af = bepsTestData.Af,
                Pac = bepsTestData.Pac,
                Ltcc = bepsTestData.Ltcc,
                Arc1 = bepsTestData.Arc1,
                Arc2 = bepsTestData.Arc2,
                Arc3 = bepsTestData.Arc3,
                Anrc1 = bepsTestData.Anrc1,
                Anrc2 = bepsTestData.Anrc2,
                CreatedAt = bepsTestData.CreatedAt,
                UpdatedAt = bepsTestData.UpdatedAt,
                Position = positionCounter
            };

            if (Items is null || Items.Count == 0)
            {
                bepsTestResult.SetColor(index: 1);
                bepsTestResult.IsDeleteButtonVisible = false;
                Items = [bepsTestResult];
            }
            else
            {
                bepsTestResult.IsDeleteButtonVisible = true;
                Items?.Add(bepsTestResult);
                bepsTestResult.SetColor(index: Items?.Count ?? 0);
            }

            SetChartItemsByIndex(Items?.Count ?? 0, bepsTestResult);

            IsAddTestButtonVisible = Items?.Count < 4;
        }
        else
        {
            await ShowMessageError("Failed to load BEPS test data.");
        }
    }

    private ObservableCollection<BeepScoresModel> CreateChartItems(BepsTestResultModel model)
    {
        return
        [
            new (BepsScoresEnum.AF,   model.Af),
            new (BepsScoresEnum.PAC,  model.Pac),
            new (BepsScoresEnum.LTCCC, model.Ltcc),
            new (BepsScoresEnum.ARC3,  model.Arc3),
            new (BepsScoresEnum.ARC2,  model.Arc2),
            new (BepsScoresEnum.ARC1,  model.Arc1),
            new (BepsScoresEnum.ANRC2, model.Anrc2),
            new (BepsScoresEnum.ANRC1, model.Anrc1)
        ];
    }

    private void SetChartItemsByIndex(int index, BepsTestResultModel beepScoresModel)
    {
        switch (index)
        {
            case 1:
                ChartItemsTest1 = CreateChartItems(beepScoresModel);
                break;
            case 2:
                ChartItemsTest2 = CreateChartItems(beepScoresModel);
                break;
            case 3:
                ChartItemsTest3 = CreateChartItems(beepScoresModel);
                break;
            case 4:
                ChartItemsTest4 = CreateChartItems(beepScoresModel);
                break;
        }
    }

    private void RemoveChartItemsByIndex(int index)
    {
        switch (index)
        {
            case 2:
                ChartItemsTest2 = [];
                break;
            case 3:
                ChartItemsTest3 = [];
                break;
            case 4:
                ChartItemsTest4 = [];
                break;
        }
    }

    public override Task OnNavigatingTo(object? parameter)
    {
        if (parameter is SportEnum sport)
            SelectedSport = sport;

        return base.OnNavigatingTo(parameter);
    }

}
