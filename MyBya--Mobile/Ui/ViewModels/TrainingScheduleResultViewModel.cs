using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Text;
using System.Text.Json;
using System.Windows.Input;
using CommunityToolkit.Maui.Core.Extensions;
using CommunityToolkit.Mvvm.Input;
using MyBya.Constants;
using MyBya.DataAccess.Interfaces;
using MyBya.Entities;
using MyBya.Enums;
using MyBya.Helpers;
using MyBya.Models;
using MyBya.Parameters;
using MyBya.Repository.Interface;
using MyBya.Services;
using MyBya.Shared;
using MyBya.Ui.Pages;
using MyBya.Ui.ViewModels.Common;
using Newtonsoft.Json.Linq;
using Serilog;
namespace MyBya.Ui.ViewModels;

public partial class TrainingScheduleResultViewModel : ViewModelBase
{
    private List<TrainingPlanDetailModel> _workouts;
    private ObservableCollection<WorkoutsModel> workouts;
    public ObservableCollection<WorkoutsModel> Workouts
    {
        get => workouts;
        set => SetProperty(ref workouts, value);
    }
    private string titleText;
    public string TitleText
    {
        get => titleText;
        set => SetProperty(ref titleText, value);
    }

    private bool isRunningTargetAvailable;
    public bool IsRunningTargetAvailable
    {
        get => isRunningTargetAvailable;
        set => SetProperty(ref isRunningTargetAvailable, value);
    }

    private bool isRowingTargetAvailable;
    public bool IsRowingTargetAvailable
    {
        get => isRowingTargetAvailable;
        set => SetProperty(ref isRowingTargetAvailable, value);
    }

    private bool isCyclingTargetAvailable;
    public bool IsCyclingTargetAvailable
    {
        get => isCyclingTargetAvailable;
        set => SetProperty(ref isCyclingTargetAvailable, value);
    }

    private bool isSwimmingTargetAvailable;
    public bool IsSwimmingTargetAvailable
    {
        get => isSwimmingTargetAvailable;
        set => SetProperty(ref isSwimmingTargetAvailable, value);
    }

    private string editorUserComment;
    public string EditorUserComment
    {
        get => editorUserComment;
        set => SetProperty(ref editorUserComment, value);
    }

    private string userComment;
    public string UserComment
    {
        get => userComment;
        set => SetProperty(ref userComment, value);
    }

    private ObservableCollection<Comments> workoutComments;
    public ObservableCollection<Comments> WorkoutComments
    {
        get => workoutComments;
        set => SetProperty(ref workoutComments, value);
    }

    private bool isCommentSectionVisible;
    public bool IsCommentSectionVisible
    {
        get => isCommentSectionVisible;
        set => SetProperty(ref isCommentSectionVisible, value);
    }

    private bool isCommentEditorVisible;
    public bool IsCommentEditorVisible
    {
        get => isCommentEditorVisible;
        set => SetProperty(ref isCommentEditorVisible, value);
    }

    private bool isSaveCommentButtonVisible;
    public bool IsSaveCommentButtonVisible
    {
        get => isSaveCommentButtonVisible;
        set => SetProperty(ref isSaveCommentButtonVisible, value);
    }

    private bool isEditCommentButtonVisible;
    public bool IsEditCommentButtonVisible
    {
        get => isEditCommentButtonVisible;
        set => SetProperty(ref isEditCommentButtonVisible, value);
    }

    private bool IsEditing { get; set; }

    public ICommand RunningTargetCommand { get; set; }
    public ICommand SwimmingTargetCommand { get; set; }
    public ICommand RowingTargetCommand { get; set; }
    public ICommand CyclingTargetCommand { get; set; }
    public IAsyncRelayCommand SaveCommentCommand { get; set; }
    public ICommand EditCommentCommand { get; set; }

    private Dictionary<string, string> fixedDictionary { get; set; }

    public TrainingScheduleResultViewModel()
    {
        _workouts = new List<TrainingPlanDetailModel>();
        workouts = new ObservableCollection<WorkoutsModel>();
        titleText = string.Empty;
        Workouts = new ObservableCollection<WorkoutsModel>();
        WorkoutComments = new ObservableCollection<Comments>();
        fixedDictionary = new Dictionary<string, string>();
        RunningTargetCommand = new Command(OnRunningTarget);
        SwimmingTargetCommand = new Command(OnSwimmingTarget);
        RowingTargetCommand = new Command(OnRowingTarget);
        CyclingTargetCommand = new Command(OnCyclingTarget);
        SaveCommentCommand = new AsyncRelayCommand(SaveCommentAsync);
        EditCommentCommand = new Command(OnEditComment);
        IsCommentSectionVisible = true;
        IsSaveCommentButtonVisible = true;
        IsCommentEditorVisible = true;
    }

    private void OnEditComment()
    {
        EditorUserComment = UserComment;
        IsEditing = true;
        IsEditCommentButtonVisible = false;
    }

    private async Task SaveCommentAsync()
    {
        try
        {

            IsBusy = true;

            if (string.IsNullOrEmpty(EditorUserComment))
            {
                await _alertService.ShowAlertAsync("MyBYa", "Please enter a comment before proceeding.");
                return;
            }

            var trainingPlanId = _workouts.FirstOrDefault()?.TrainingPlanId ?? 0;
            var workout = _workouts.FirstOrDefault();

            if (workout?.Date == null)
            {
                await _alertService.ShowAlertAsync("MyBYa", "Unable to save comment: workout date is missing.");
                return;
            }

            if (IsEditing)
            {
                bool successUpdate = await ServiceHelper
                    .GetService<AthleteTrainingCommentService>()
                    .SaveUserComment(EditorUserComment, trainingPlanId, workout.Date.Value);

                if (successUpdate)
                {
                    await ShowToastMessage("Comment updated successfully.");
                    UserComment = EditorUserComment;
                    EditorUserComment = string.Empty;
                    IsEditCommentButtonVisible = true;
                }
                else
                {
                    await _alertService.ShowAlertAsync("MyBYa", "Failed to update comment. Please try again.");
                }

                return;
            }

            bool success = await ServiceHelper
                .GetService<AthleteTrainingCommentService>()
                .SaveUserComment(EditorUserComment, trainingPlanId, workout.Date.Value);

            if (success)
            {
                await ShowToastMessage("Comment saved successfully.");
                IsEditCommentButtonVisible = true;
                UserComment = EditorUserComment;
                EditorUserComment = string.Empty;
            }
            else
            {
                await _alertService.ShowAlertAsync("MyBYa", "Failed to save comment. Please try again.");
            }
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            await ShowMessageError();
        }
        finally
        {
            IsBusy = false;
        }
    }

    private void OnRunningTarget()
    {
        _navigationService.NavigateToPage<TrainingTargetsPage>(SportEnum.RUNNING);
    }

    private void OnSwimmingTarget()
    {
        _navigationService.NavigateToPage<TrainingTargetsPage>(SportEnum.SWIMMING);
    }

    private void OnRowingTarget()
    {
        _navigationService.NavigateToPage<TrainingTargetsPage>(SportEnum.ROWING);
    }

    private void OnCyclingTarget()
    {
        _navigationService.NavigateToPage<TrainingTargetsPage>(SportEnum.CYCLING);
    }

    public override Task OnNavigatingTo(object? parameter)
    {
        var trainingPlanDetail = parameter as TrainingPlanDetailParameters;

        if (trainingPlanDetail == null)
            return Task.CompletedTask;

        TitleText = trainingPlanDetail.SelectedDate ?? string.Empty;
        _workouts = trainingPlanDetail.Workouts ?? new List<TrainingPlanDetailModel>();

        return base.OnNavigatingTo(parameter);
    }

    private async Task ProcessWorkout(string workoutName)
    {
        var valuePairs = BuildWorkoutDictionary(workoutName);
        var currentWorkout = _workouts.FirstOrDefault(x => x.WorkoutName == workoutName);

        List<WorkoutLabel> workoutLabels = [];
        string workoutDescription = currentWorkout?.WorkoutDescription ?? "";

        string comment = currentWorkout?.Comment ?? "";

        var configArray = JArray.Parse(currentWorkout?.Configurable ?? "");
        var fieldsArray = (JArray)((JArray)configArray[0])[1];

        ProcessFixed(currentWorkout);
        ProcessWorkoutFields(fieldsArray, valuePairs);

        foreach (var fixedItem in fixedDictionary)
        {
            if (fixedItem.Key.ToLower() == "weekly sessions" ||
                fixedItem.Key.ToLower() == "session volume")
            {
                continue;
            }

            switch (fixedItem.Key)
            {
                case "Target Heartrate":
                    await ProcessHeartRate(fixedDictionary["Target Heartrate"], workoutLabels);
                    break;
                case "Pace":
                    await ProcessPace(fixedDictionary, valuePairs, workoutLabels);
                    break;
                default:
                    workoutLabels.Add(new WorkoutLabel
                    {
                        Text = $"{fixedItem.Key}\n{fixedItem.Value}"
                    });
                    break;
            }
        }

        Workouts.Add(new WorkoutsModel
        {
            WorkoutName = workoutName,
            WorkoutDescription = workoutDescription,
            WorkoutLabels = workoutLabels,
            WorkoutComment = comment
        });
    }

    private Dictionary<string, string> BuildWorkoutDictionary(string workoutName)
    {
        var workoutList = _workouts
            .Where(w => w.WorkoutName == workoutName && w.FieldName != null && w.FieldValue != null)
            .ToList();

        var valuePairs = new Dictionary<string, string>();

        foreach (var workout in workoutList)
        {
            if (!string.IsNullOrEmpty(workout.FieldName) &&
                !string.IsNullOrEmpty(workout.FieldValue))
            {
                if (!valuePairs.ContainsKey(workout.FieldName))
                    valuePairs.Add(workout.FieldName!, workout.FieldValue);
            }
        }

        return valuePairs;
    }

    private void ProcessFixed(TrainingPlanDetailModel? currentWorkout)
    {
        fixedDictionary.Clear();
        string fixedJson = currentWorkout?.Fixed ?? "";
        var list = JsonSerializer.Deserialize<List<List<string>>>(fixedJson);

        if (list == null || list.Count == 0)
            return;

        foreach (var pair in list)
        {
            if (pair.Count == 2)
            {
                string label = pair[0];
                string value = pair[1];
                fixedDictionary.Add(label, value);
            }
        }
    }

    private void ProcessWorkoutFields(JArray fieldsArray, Dictionary<string, string> dictionary)
    {
        foreach (var entry in fieldsArray)
        {
            var fieldName = entry[0]?.ToString();
            var cfg = entry[1];

            if (fieldName != null && !IsValidField(fieldName, dictionary))
                continue;

            if (fieldName == "target_heartrate")
            {
                fixedDictionary["Target Heartrate"] = dictionary[fieldName];
            }
            else if (fieldName == "pace")
            {
                fixedDictionary["Pace"] = dictionary[fieldName];
            }
            else if (fieldName == "session_volume")
            {
                fixedDictionary["Session Volume"] = dictionary[fieldName];
            }
            else if (cfg != null && cfg.Type == JTokenType.Object && cfg["label"] != null)
                if (fieldName != null && dictionary.ContainsKey(fieldName))
                {
                    ProcessGenericField(cfg, dictionary[fieldName]);
                }
        }
    }
    private bool IsValidField(string fieldName, Dictionary<string, string> dictionary)
    {
        return !string.IsNullOrWhiteSpace(fieldName)
            && dictionary.ContainsKey(fieldName)
            && !string.IsNullOrEmpty(dictionary[fieldName]);
    }

    private async Task ProcessHeartRate(string heartRateZones, List<WorkoutLabel> workoutLabels)
    {
        if (heartRateZones == "N/A")
            return;

        var currentTestSetup = MyByaContext.Instance.CurrentTestSetup;

        if (currentTestSetup?.Sport == null)
        {
            Log.Logger.Warning("Unable to process heart rate: Current test setup or sport is not available");
            return;
        }

        var heartRates = await ServiceHelper
            .GetService<ITestResultHeartRateRepository>()
            .SelectByCriteria(x => x.Sport == currentTestSetup.Sport);

        if (heartRates == null || !heartRates.Any())
        {
            Log.Logger.Warning("No heart rate data found for sport {Sport}", currentTestSetup.Sport);
            return;
        }

        var heartRateItem = heartRates.First();

        string result = "";

        if (heartRateZones.Contains("-"))
        {
            foreach (var zone in heartRateZones.Split('-'))
                result += GetHeartRateZoneText(zone.Trim(), heartRateItem);
        }
        else
        {
            result = GetHeartRateZoneText(heartRateZones.Trim(), heartRateItem);
        }

        workoutLabels.Add(new WorkoutLabel
        {
            Text = $"Target Heartrate\n{heartRateZones}: {result}"
        });
    }

    private string GetHeartRateZoneText(string zone, TestResultHeartRatesEntity heartRateItem) => zone switch
    {
        HeartRateZonesConstants.ZONE_1 => $"< {heartRateItem.Zone1} - ",
        HeartRateZonesConstants.ZONE_2_LOW => $"[{heartRateItem.Zone2Low}]",
        HeartRateZonesConstants.ZONE_2_MID => $"[{heartRateItem.Zone2Mid}]",
        HeartRateZonesConstants.ZONE_2_HIGH => $"[{heartRateItem.Zone2High}]",
        HeartRateZonesConstants.ZONE_3 => $"[{heartRateItem.Zone3}]",
        _ => string.Empty
    };

    private async Task ProcessPace(Dictionary<string, string> fixedDictionary,
        Dictionary<string, string> valuePairs,
        List<WorkoutLabel> workoutLabels)
    {
        if (!valuePairs.TryGetValue("interval_distance", out var intervalDistance))
            return;

        if (!fixedDictionary.TryGetValue("Pace", out var paceFieldName))
            return;

        var paces = await ServiceHelper
            .GetService<ITestResultPaceChartRepository>()
            .GetPacesByInterval(intervalDistance);

        string paceText = GetPaceText(paceFieldName, intervalDistance, paces);

        if (!string.IsNullOrEmpty(paceText))
        {
            workoutLabels.Add(new WorkoutLabel
            {
                Text = $"Interval Distance\n{intervalDistance}"
            });

            workoutLabels.Add(new WorkoutLabel
            {
                Text = $"Pace\n{paceText}"
            });
        }
    }

    private string GetPaceText(string paceType, string distance, TestResultPaceChartModel pace)
    {
        return paceType switch
        {
            PacesConstants.ARC1 => $"{paceType} ({pace.Arc1Min} - {pace.Arc1Max})",
            PacesConstants.ARC2 => $"{paceType} ({pace.Arc2Min} - {pace.Arc2Max})",
            PacesConstants.ARC3 => $"{paceType} ({pace.Arc3Min} - {pace.Arc3Max})",
            PacesConstants.LTCC => $"{paceType} ({pace.LtccMin} - {pace.LtccMax})",
            _ => string.Empty
        };
    }

    private void ProcessSessionVolume(string sessionVolumeValue, List<WorkoutLabel> workoutLabels)
    {
        workoutLabels.Add(new WorkoutLabel
        {
            Text = $"Session Volume\n{sessionVolumeValue}"
        });
    }

    private void ProcessGenericField(JToken cfg, string fieldValue)
    {
        string label = cfg.Value<string>("label") ?? string.Empty;
        fixedDictionary[label] = fieldValue;
    }

    public override async Task OnAppearing()
    {
        try
        {
            IsBusy = true;

            await base.OnAppearing();
            await SetupWorkouts();
            await SetupViewTargetsButtons();
            await SetupUserComment();
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
        }
        finally
        {
            IsBusy = false;
        }
    }

    private async Task SetupUserComment()
    {
        int memberId = MyByaContext.Instance.GetMemberId();
        int trainingPlanId = _workouts.FirstOrDefault()?.TrainingPlanId ?? 0;
        DateTime? date = _workouts.FirstOrDefault()?.Date;

        var commments = await ServiceHelper
            .GetService<AthleteTrainingCommentService>()
            .GetCommentsByDate(memberId, date?.ToString("yyyy-MM-dd") ?? "", trainingPlanId);

        if (commments != null && commments.Count > 0)
        {
            IsEditCommentButtonVisible = true;
            UserComment = commments.FirstOrDefault()?.Comment ?? "";
        }
    }

    private async Task SetupViewTargetsButtons()
    {
        var athleteTests = await ServiceHelper
            .GetService<IAthleteTestSetupRepository>()
            .SelectAllItems();

        if (athleteTests == null || !athleteTests.Any())
        {
            Log.Logger.Warning("No athlete tests found.");
            return;
        }

        foreach (var test in athleteTests)
        {
            if (test.Sport == (int)SportEnum.RUNNING)
                IsRunningTargetAvailable = true;
            else if (test.Sport == (int)SportEnum.ROWING)
                IsRowingTargetAvailable = true;
            else if (test.Sport == (int)SportEnum.CYCLING)
                IsCyclingTargetAvailable = true;
            else if (test.Sport == (int)SportEnum.SWIMMING)
                IsSwimmingTargetAvailable = true;
        }
    }

    private async Task SetupWorkouts()
    {
        List<string> workoutNames = _workouts != null
            ? _workouts
                .Select(x => x.WorkoutName)
                .Where(name => name != null)
                .Distinct()
                .Cast<string>()
                .ToList()
            : new List<string>();

        if (workoutNames is null)
            return;

        if (workoutNames.FirstOrDefault() == "Day Off")
        {
            Workouts.Add(new WorkoutsModel
            {
                WorkoutLabels = [new WorkoutLabel { Text = "Day Off" }]
            });

            IsCommentSectionVisible = false;

            return;
        }

        foreach (string workoutName in workoutNames)
        {
            await ProcessWorkout(workoutName);
        }
    }

    protected override void ViewModelBase_PropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        base.ViewModelBase_PropertyChanged(sender, e);

        if (e.PropertyName == nameof(IsEditCommentButtonVisible))
        {
            IsSaveCommentButtonVisible = !IsEditCommentButtonVisible;
            IsCommentEditorVisible = !IsEditCommentButtonVisible;
        }
    }
}
