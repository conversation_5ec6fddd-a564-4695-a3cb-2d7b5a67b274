using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using AutoMapper;
using CommunityToolkit.Maui.Alerts;
using CommunityToolkit.Maui.Core;
using CommunityToolkit.Maui.Views;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using MyBya.Constants;
using MyBya.Helpers;
using MyBya.Interfaces;
using MyBya.Singletons;
using MyBya.Ui.Controls;
using MyBya.Ui.Views.Popups;
using Serilog;

namespace MyBya.Ui.ViewModels.Common;

public abstract class ViewModelBase : INotifyPropertyChanged
{
    /// <summary>
    /// Occurs when property changed.
    /// </summary>
    public new event PropertyChangedEventHandler? PropertyChanged;
    public readonly IAlertService _alertService;
    public readonly INavigationService _navigationService;
    private Spinner? _spinner { get; set; }
    protected IMapper Mapper { get; set; }
    public IAsyncRelayCommand BackCommand { get; set; }
    private Popup? currentOpenedPopup { get; set; }

    private bool _isBusy;
    public bool IsBusy
    {
        get => _isBusy;
        set => SetProperty(ref _isBusy, value);
    }

    private bool openLoadingView;
    public bool OpenLoadingView
    {
        get => openLoadingView;
        set => SetProperty(ref openLoadingView, value);
    }

    protected bool IsLoaded { get; private set; }

    protected ViewModelBase()
    {
        _alertService = ServiceHelper.GetService<IAlertService>();
        _navigationService = ServiceHelper.GetService<INavigationService>();
        PropertyChanged += ViewModelBase_PropertyChanged;
        Mapper = ServiceHelper.GetService<IMapper>();
        BackCommand = new AsyncRelayCommand(NavigateBack);
    }

    protected virtual async Task NavigateBack()
    {
        await _navigationService.NavigateBack();
    }

    protected virtual async void ViewModelBase_PropertyChanged(object? sender,
        PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(IsBusy))
        {
            await ShowLoadingView();
        }
        else if (e.PropertyName == nameof(OpenLoadingView))
        {
            await ExecuteOpenLoadingView();
        }
    }

    public virtual Task OnNavigatingTo(object? parameter) => Task.CompletedTask;

    public virtual Task OnNavigatedFrom(bool isForwardNavigation) => Task.CompletedTask;

    public virtual Task OnNavigatedTo() => Task.CompletedTask;

    public virtual void RaisePropertyChanged([CallerMemberName] string? property = null) => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(property));

    public virtual Task OnAppearing()
    {
        IsLoaded = true;
        return Task.CompletedTask;
    }

    /// <summary>
    /// Sets the property.
    /// </summary>
    /// <returns><c>true</c>, if property was set, <c>false</c> otherwise.</returns>
    /// <param name="backingStore">Backing store.</param>
    /// <param name="value">Value.</param>
    /// <param name="validateValue">Validates value.</param>
    /// <param name="propertyName">Property name.</param>
    /// <param name="onChanged">On changed.</param>
    /// <typeparam name="T">The 1st type parameter.</typeparam>
    protected virtual bool SetProperty<T>(ref T backingStore, T value, [CallerMemberName] string propertyName = "", Action? onChanged = null, Func<T, T, bool>? validateValue = null)
    {
        if (EqualityComparer<T>.Default.Equals(backingStore, value))
        {
            return false;
        }
        if (validateValue != null && !validateValue!(backingStore, value))
        {
            return false;
        }

        backingStore = value;
        onChanged?.Invoke();
        OnPropertyChanged(propertyName);
        return true;
    }

    /// <summary>
    /// Raises the property changed event.
    /// </summary>
    /// <param name="propertyName">Property name.</param>
    protected new virtual void OnPropertyChanged([CallerMemberName] string propertyName = "")
    {
        this.PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    protected async Task ShowLoadingView()
    {
        try
        {
            Page? page = NavigationHelper.GetCurrentPage();

            if (IsBusy)
            {
                _spinner = ServiceHelper.GetService<Spinner>();

                if (page != null && _spinner != null)
                {
                    await page.ShowPopupAsync(_spinner);
                }
            }
            else
            {
                if (_spinner != null)
                {
                    await _spinner.CloseAsync();
                    _spinner = null;
                }
            }
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            _spinner = null;
        }
    }

    public async Task ShowMessageError(string message = "")
    {
        if (string.IsNullOrEmpty(message))
        {
            await _alertService.ShowAlertAsync("MyBya", FriendlyUserTextsConstants.GERENERIC_ERROR);
        }
        else
        {
            await _alertService.ShowAlertAsync("MyBya", message);
        }
    }

    protected async Task ShowToastMessage(string message)
    {
        await MainThread.InvokeOnMainThreadAsync(async () =>
        {
            double fontSize = 14;
            var toast = Toast.Make(message, ToastDuration.Long, fontSize);
            await toast.Show();
        });
    }

    protected virtual async Task<bool> DeviceHasInternetConnection()
    {
        bool hasInternet = await AppManager.Instance
            .IsInternetConnected();

        if (!hasInternet)
        {
            string message = "It looks like you're offline. Please check your internet connection and try again.";
            await _alertService.ShowAlertAsync("", message);
        }

        return hasInternet;
    }

    protected async Task ExecuteOpenLoadingView()
    {
        if (OpenLoadingView)
            await _navigationService.NavigateToModal<LoadingView>();
        else
            await _navigationService.CloseModal();
    }

    protected virtual async Task ShowPopupAsync<TPopup>(TPopup? popupInstance = null)
        where TPopup : Popup
    {
        Page? page = NavigationHelper.GetCurrentPage();

        if (page == null)
        {
            Log.Logger.Warning("Current page is null. Cannot show popup.");
            return;
        }

        if (popupInstance is null)
            currentOpenedPopup = ServiceHelper.GetService<TPopup>();
        else
            currentOpenedPopup = popupInstance;

        await page.ShowPopupAsync(currentOpenedPopup);
    }

    protected virtual async Task ClosePopupAsync()
    {
        if (currentOpenedPopup != null)
        {
            await currentOpenedPopup.CloseAsync();
            currentOpenedPopup = null;
        }
    }

        protected virtual async Task<bool> ShowConfirmationPopup(string title, string message)
        {
            var tcs = new TaskCompletionSource<bool>();

            var popup = new ConfirmationPopup();
            popup.AddTitle(title);
            popup.AddMessage(message);
            popup.OnConfirm = tcs.SetResult;

            await ShowPopupAsync(popup);
            return await tcs.Task;
        }

}
