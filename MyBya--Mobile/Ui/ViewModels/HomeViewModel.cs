using System.Windows.Input;
using MyBya.Ui.ViewModels.Common;
using MyBya.Ui.Pages;
using MyBya.Shared;
using Serilog;
using MyBya.Helpers;
using MyBya.Services;
using MyBya.DataAccess.Interfaces;
using MyBya.Repository;
using MyBya.Repository.Interface;
using MyBya.Entities;
using MyBya.Models;
using MyBya.Constants;

namespace MyBya.Ui.ViewModels;

public class HomeViewModel : ViewModelBase
{
    private string _welcomeMessage = "Welcome to MyBya";
    public string WelcomeMessage
    {
        get => _welcomeMessage;
        set => SetProperty(ref _welcomeMessage, value);
    }

    public ICommand NavigateToTestProtocolCommand { get; }
    public ICommand NavigateToCalendarCommand { get; }
    public ICommand NavigateToHistoryCommand { get; }
    public ICommand NavigateToAccountCommand { get; }

    public HomeViewModel()
    {
        NavigateToTestProtocolCommand = new Command(OnNavigateToTestProtocol);
        NavigateToCalendarCommand = new Command(OnNavigateToCalendar);
        NavigateToHistoryCommand = new Command(OnNavigateToHistory);
        NavigateToAccountCommand = new Command(OnNavigateToAccount);
    }

    public override async Task OnAppearing()
    {
        await base.OnAppearing();
        await SetUserInfo();
        SyncAthleteData();
    }

    private void SyncAthleteData()
    {
        var syncTask = Task.Run(async () =>
        {
            try
            {
                var athleteTestsSetups = await GetMostRecentTests();

                if (athleteTestsSetups?.Count > 0)
                {
                    var mostRecentTest = athleteTestsSetups
                        .OrderByDescending(x => x.CreationTime)
                        .FirstOrDefault();

                    if (mostRecentTest == null)
                    {
                        Log.Logger.Information("No recent athlete tests found.");
                        return;
                    }

                    //Caching the most recent test setup for now, need to review when having all sports.
                    MyByaContext.Instance.SetCurrentTestSetup(mostRecentTest);

                    await ProcessAthleteTests(athleteTestsSetups);
                }
            }
            catch (Exception ex)
            {
                Log.Logger.Error(ex, ex.Message);
                throw;
            }
        })
        .ContinueWith(task =>
        {
            if (task.Exception != null)
                Log.Logger.Error(task.Exception, "Error occurred during athlete data synchronization.");
        });
    }

    private async Task SetUserInfo()
    {
        try
        {
            IsBusy = true;

            string? abpUserId = await SecureStorage.Default
                .GetAsync(SecureStorageConstants.AbpUserId);

            if (string.IsNullOrEmpty(abpUserId))
            {
                Log.Logger.Error("abpUserId not found in secure storage.");
                return;
            }

            var user = await ServiceHelper
                .GetService<AppUserService>()
                .GetAbpUserById(abpUserId);

            if (user == null)
                throw new Exception("User not found in the system.");

            MyByaContext.Instance.SetCurrentUser(user);
            await MyByaContext.Instance.SetMemberId();
            await MyByaContext.Instance.SetUserId();
            
            // Update welcome message with user's name
            var firstName = MyByaContext.Instance.CurrentUser?.Name;
            WelcomeMessage = !string.IsNullOrEmpty(firstName) ? $"Hi {firstName}!" : "Welcome to MyBya";
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            await ShowMessageError("Failed to load user information. Please try again.");
        }
        finally
        {
            IsBusy = false;
        }
    }

    private async Task<List<AthleteTestSetupModel>> GetMostRecentTests()
    {
        return await ServiceHelper
            .GetService<AthleteTestSetupService>()
            .GetMostRecentTestsByMemberId(MyByaContext.Instance.GetMemberId());
    }

    private async Task ProcessAthleteTests(List<AthleteTestSetupModel> items)
    {
        foreach (var item in items)
        {
            var test = CreateTestEntity(item);
            var localTestSetup = await GetLocalTestSetup(item);

            await SyncTestSetup(item, localTestSetup);

            bool isOutdated = IsTestDataOutdated(localTestSetup, item);
            await SyncTestData(test, isOutdated);
        }
    }

    private AthleteTestSetupEntity CreateTestEntity(AthleteTestSetupModel item)
    {
        return new AthleteTestSetupEntity
        {
            Id = item.Id,
            Sport = item.Sport,
            TestDetailAthleteId = item.TestDetailAthleteId
        };
    }

    private async Task<IList<AthleteTestSetupEntity>> GetLocalTestSetup(AthleteTestSetupModel item)
    {
        return await ServiceHelper
            .GetService<IAthleteTestSetupRepository>()
            .SelectByCriteria(x => x.Id == item.Id && x.Sport == item.Sport);
    }

    private async Task SyncTestSetup(AthleteTestSetupModel item, IList<AthleteTestSetupEntity> localTestSetup)
    {
        if (localTestSetup?.Count == 0)
        {
            var newTestSetup = Mapper.Map<AthleteTestSetupEntity>(item);

            await ServiceHelper
                .GetService<IAthleteTestSetupRepository>()
                .Insert(newTestSetup);
        }
    }

    private bool IsTestDataOutdated(IList<AthleteTestSetupEntity> localTestSetup, AthleteTestSetupModel item)
    {
        return localTestSetup != null &&
            (localTestSetup.FirstOrDefault()?.CreationTime < item.CreationTime ||
             localTestSetup.FirstOrDefault()?.UpdatedAt < item.UpdatedAt ||
             localTestSetup.FirstOrDefault()?.LastModificationTime < item.LastModificationTime);
    }

    private async Task SyncTestData(AthleteTestSetupEntity test, bool isOutdated)
    {
        await SyncPaceCharts(test, isOutdated);
        await SyncHeartRates(test, isOutdated);
    }

    private async Task SyncPaceCharts(AthleteTestSetupEntity test, bool isOutdated)
    {
        var paces = await ServiceHelper
            .GetService<ITestResultPaceChartRepository>()
            .SelectByCriteria(x => x.AthleteTestSetupId == test.Id && x.Sport == test.Sport);

        if (paces?.Count == 0 || isOutdated)
        {
            paces?.Clear();
            await PaceChartProcessingShared.BuildPaceCharts(test);
        }
    }

    private async Task SyncHeartRates(AthleteTestSetupEntity test, bool isOutdated)
    {
        var heartRates = await ServiceHelper
            .GetService<ITestResultHeartRateRepository>()
            .SelectByCriteria(x => x.AthleteTestSetupId == test.Id && x.Sport == test.Sport);

        if (heartRates?.Count == 0 || isOutdated)
        {
            heartRates?.Clear();
            await HeartRateProcessingShared.SetHeartRates(test);
        }
    }

    private void OnNavigateToTestProtocol()
    {
        if (Application.Current?.MainPage is MainTabbedPage tabbedPage)
        {
            tabbedPage.CurrentPage = tabbedPage.Children[1];
        }
    }

    private void OnNavigateToCalendar()
    {
        if (Application.Current?.MainPage is MainTabbedPage tabbedPage)
        {
            tabbedPage.CurrentPage = tabbedPage.Children[2];
        }
    }

    private void OnNavigateToHistory()
    {
         if (Application.Current?.MainPage is MainTabbedPage tabbedPage)
        {
            tabbedPage.CurrentPage = tabbedPage.Children[3];
        }
    }

    private void OnNavigateToAccount()
    {
        if (Application.Current?.MainPage is MainTabbedPage tabbedPage)
        {
            tabbedPage.CurrentPage = tabbedPage.Children[4];
        }
    }
}
