using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using MyBya.Helpers;
using MyBya.Models;
using MyBya.Parameters;
using MyBya.Services;
using MyBya.Services.DTOs;
using MyBya.Shared;
using MyBya.Ui.Controls;
using MyBya.Ui.Pages;
using MyBya.Ui.ViewModels.Common;
using Serilog;
using Syncfusion.Maui.Toolkit.Calendar;

namespace MyBya.Ui.ViewModels;

public class TrainingScheduleViewModel : ListViewModelBase<WorkoutDays>
{
    public IAsyncRelayCommand ContinueCommand { get; }

    private string headerDateText;
    public string HeaderDateText
    {
        get => headerDateText;
        set => SetProperty(ref headerDateText, value);
    }

    private DateTime selectedDate;
    public DateTime SelectedDate
    {
        get => selectedDate;
        set
        {
            SetProperty(ref selectedDate, value);
            HeaderDateText = value.ToString("MMMM yyyy");
        }
    }

    private string weekDayRange;
    public string WeekDayRange
    {
        get => weekDayRange;
        set => SetProperty(ref weekDayRange, value);
    }

    public IAsyncRelayCommand SelectDayCommand { get; set; }
    public IAsyncRelayCommand CalendarSelectionChangedCommand { get; set; }


    public TrainingScheduleViewModel()
    {
        headerDateText = string.Empty;
        ContinueCommand = new AsyncRelayCommand<object>(ContinueAsync);
        SelectDayCommand = new AsyncRelayCommand<WorkoutDays>(OnSelectedItemChanged);
        CalendarSelectionChangedCommand = new AsyncRelayCommand(OnCalendarSelectionChanged);
        SelectedDate = DateTime.Now;
    }

    private async Task OnCalendarSelectionChanged()
    {
        if (IsLoaded)
        {
            var items = await GetWeekTrainingModels();
            Items = new ObservableCollection<WorkoutDays>(items);
        }
    }

    private async Task ContinueAsync(object? value)
    {
        if (value is null)
            return;

        string valueString = value?.ToString() ?? string.Empty;

        if (string.IsNullOrEmpty(valueString))
            return;

        string convertedDate = DateTime.Parse(valueString).ToString("yyyy-MM-dd");

        List<TrainingPlanDetailModel> workouts = await ServiceHelper
            .GetService<TrainingPlanDetailService>()
            .GetTrainingPlansByMemberIdAndDate(memberId: MyByaContext.Instance.GetMemberId(), convertedDate);

        if (workouts is null || workouts.Count == 0)
        {
            await _alertService.ShowAlertAsync("MyBYa", "No workouts found for the selected date.");
            return;
        }

        var trainingPlanDetailDTO = new TrainingPlanDetailParameters()
        {
            SelectedDate = DateTime.Parse(valueString).ToLongDateString(),
            Workouts = workouts
        };

        await _navigationService.NavigateToPage<TrainingScheduleResultPage>(trainingPlanDetailDTO);
    }

    public async Task<List<WorkoutDays>> GetWeekTrainingModels()
    {
        try
        {
            IsBusy = true;

            var service = ServiceHelper.GetService<TrainingPlanCalendarService>();

            WorkoutScheduleModel weekView = await service
                .GetWeekViewByMemberIdAndDate(MyByaContext.Instance.GetMemberId(), SelectedDate);

            if (weekView is null || weekView.Days is null || weekView.Days.Count == 0)
            {
                await _alertService.ShowAlertAsync("MyBYa", "No workout days found for the selected week.");
                return new List<WorkoutDays>();
            }

            for (int i = 0; i < weekView.Days.Count; i++)
            {
                if (i % 2 == 0)
                {
                    weekView.Days[i].CardBackgroundColor = Color.FromArgb("#F1EFF5");
                }
            }

            return weekView.Days;
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            await ShowMessageError();
            return [];
        }
        finally
        {
            IsBusy = false;
        }
    }

    protected override void ViewModelBase_PropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        base.ViewModelBase_PropertyChanged(sender, e);

        if (e.PropertyName == nameof(SelectedDate))
        {
            SetWeekDayRange();
        }
    }

    private void SetWeekDayRange()
    {
        try
        {
            var date = SelectedDate.AddDays(6);

            if (SelectedDate.Month == date.Month)
            {
                WeekDayRange = $"{SelectedDate:MMMM} {SelectedDate.Day} - {date.Day}";
            }
            else
            {
                WeekDayRange = $"{SelectedDate:MMMM} {SelectedDate.Day} - {date:MMMM} {date.Day}";
            }
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
        }
    }

    public async Task OnSelectedItemChanged(WorkoutDays? selectedDay)
    {
        try
        {
            if (selectedDay is null)
                return;

            IsBusy = true;

            DeselectAllItems();
            SelectCurrentItem(selectedDay);
            await ContinueAsync(selectedDay.Date);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
        }
        finally
        {
            IsBusy = false;
        }
    }

    private void SelectCurrentItem(WorkoutDays selectedDay)
    {
        selectedDay.IsSelected = true;
    }

    private void DeselectAllItems()
    {
        foreach (var day in Items)
            day.IsSelected = false;
    }

    public override async Task OnAppearing()
    {
        await base.OnAppearing();

        try
        {
            var items = await GetWeekTrainingModels();

            if (items is not null && items.Count > 0)
                Items = new ObservableCollection<WorkoutDays>(items);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            await ShowMessageError();
        }
    }
}
