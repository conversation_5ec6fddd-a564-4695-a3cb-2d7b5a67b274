using System.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using MyBya.Enums;
using MyBya.Ui.Pages;
using MyBya.Ui.ViewModels.Common;

namespace MyBya.Ui.ViewModels;

public class ComparePowerScoresSelectViewModel : ViewModelBase
{
    public IAsyncRelayCommand ShowScoresCommand { get; set; }

    private SportEnum? selectedSport;
    public SportEnum? SelectedSport
    {
        get => selectedSport;
        set => SetProperty(ref selectedSport, value);
    }

    private bool isShowScoresButtonEnabled;
    public bool IsShowScoresButtonEnabled
    {
        get => isShowScoresButtonEnabled;
        set => SetProperty(ref isShowScoresButtonEnabled, value);
    }

    public ComparePowerScoresSelectViewModel()
    {
        ShowScoresCommand = new AsyncRelayCommand(ShowScores);
    }

    private async Task ShowScores()
    {
        await _navigationService.NavigateToPage<ComparePowerScoresPage>(SelectedSport);
    }

    protected override void ViewModelBase_PropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        base.ViewModelBase_PropertyChanged(sender, e);

        if (e.PropertyName == nameof(SelectedSport))
        {
            IsShowScoresButtonEnabled = selectedSport != SportEnum.NONE;
        }
    }
}
