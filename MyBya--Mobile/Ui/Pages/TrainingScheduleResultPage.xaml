﻿<?xml version="1.0" encoding="utf-8" ?>
<local:BaseContentPage
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="MyBya.Ui.Pages.TrainingScheduleResultPage"
    xmlns:local="clr-namespace:MyBya.Ui.Pages.Common"
    xmlns:viewModels="clr-namespace:MyBya.Ui.ViewModels"
    xmlns:views="clr-namespace:MyBya.Ui.Views"
    x:TypeArguments="viewModels:TrainingScheduleResultViewModel"
    xmlns:components="clr-namespace:MyBya.Ui.Views.Components"
    Title="TrainingScheduleResultPage">
    <Grid
        RowDefinitions="Auto,*">
        <views:BaseView
            HasNavigationBack="True"
            TitleText="Training Schedule"/>
        <ScrollView
            Grid.Row="1">
            <components:TopRoundedBorderLayoutView
                TitleText="{Binding TitleText}">
                <Grid
                    RowDefinitions="Auto,*,*, Auto, Auto">
                    <Label
                        FontFamily="Spartan"
                        Margin="10"
                        FontAttributes="Bold"
                        TextColor="Black"
                        FontSize="20"
                        Text="Workout instructions"/>
                    <CollectionView
                        Grid.Row="2"
                        Margin="12"
                        ItemsSource="{Binding Workouts}"
                        x:Name="collectionView">
                        <CollectionView.ItemTemplate>
                            <DataTemplate>
                                <VerticalStackLayout
                                    Margin="0,30,0,0"
                                    Spacing="10">
                                    <VerticalStackLayout
                                        Spacing="20">
                                        <Label
                                            FontFamily="Spartan"
                                            FontAttributes="Bold"
                                            FontSize="18"
                                            TextColor="Black"
                                            IsVisible="{Binding WorkoutName, Converter={StaticResource textToBoolConverter}}"
                                            Text="{Binding WorkoutName}"/>
                                        <Label
                                            IsVisible="{Binding WorkoutComment, Converter={StaticResource textToBoolConverter}}"
                                            FontFamily="Spartan"
                                            Text="{Binding WorkoutComment}"
                                            FontSize="18"
                                            TextColor="Black"/>
                                        <Label
                                            IsVisible="{Binding WorkoutDescription, Converter={StaticResource textToBoolConverter}}"
                                            FontFamily="Spartan"
                                            Text="{Binding WorkoutDescription}"
                                            FontSize="18"
                                            TextColor="#0B0B0B"/>
                                    </VerticalStackLayout>
                                    <StackLayout
                                        Margin="0,20,0,0"
                                        Spacing="20"
                                        BindableLayout.ItemsSource="{Binding WorkoutLabels}">
                                        <BindableLayout.ItemTemplate>
                                            <DataTemplate>
                                                <Label
                                                    FontFamily="Spartan"
                                                    TextColor="#0B0B0B"
                                                    FontSize="18"
                                                    IsVisible="{Binding Text, Converter={StaticResource textToBoolConverter}}"
                                                    Text="{Binding Text}"/>
                                            </DataTemplate>
                                        </BindableLayout.ItemTemplate>
                                    </StackLayout>
                                </VerticalStackLayout>
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                    </CollectionView>
                    <VerticalStackLayout
                        Grid.Row="3"
                        Margin="10,0,10,10"
                        Spacing="20">
                        <Button
                            IsVisible="{Binding IsRunningTargetAvailable}"
                            Style="{StaticResource SecondaryButton}"
                            Text="VIEW RUNNING TARGETS"
                            Command="{Binding RunningTargetCommand}"
                            FontSize="16"/>
                        <Button
                            IsVisible="{Binding IsSwimmingTargetAvailable}"
                            Style="{StaticResource SecondaryButton}"
                            Text="VIEW SWIMMING TARGETS"
                            Command="{Binding SwimmingTargetCommand}"
                            FontSize="16"/>
                        <Button
                            IsVisible="{Binding IsRowingTargetAvailable}"
                            Style="{StaticResource SecondaryButton}"
                            Text="VIEW ROWING TARGETS"
                            Command="{Binding RowingTargetCommand}"
                            FontSize="16"/>
                        <Button
                            IsVisible="{Binding IsCyclingTargetAvailable}"
                            Style="{StaticResource SecondaryButton}"
                            Text="VIEW CYCLING TARGETS"
                            Command="{Binding CyclingTargetCommand}"
                            FontSize="16"/>
                    </VerticalStackLayout>
                    <VerticalStackLayout
                        IsVisible="{Binding IsCommentSectionVisible}"
                        Grid.Row="4"
                        Spacing="10"
                        Margin="10,25,10,10">
                        <BoxView
                            HeightRequest="1"
                            BackgroundColor="LightGray"/>
                        <Label
                            FontFamily="Spartan"
                            FontAttributes="Bold"
                            TextColor="Black"
                            FontSize="20"
                            Text="Comment"/>
                        <Label
                            Margin="2,10,0,10"
                            FontFamily="Spartan"
                            TextColor="#0B0B0B"
                            FontSize="18"
                            IsVisible="{Binding UserComment, Converter={StaticResource textToBoolConverter}}"
                            Text="{Binding UserComment}"/>
                        <Editor
                            IsVisible="{Binding IsCommentEditorVisible}"
                            BackgroundColor="#EEE"
                            MinimumHeightRequest="75"
                            Text="{Binding EditorUserComment}"/>
                        <Button
                            IsVisible="{Binding IsSaveCommentButtonVisible}"
                            Style="{StaticResource PrimaryButton}"
                            Text="SAVE COMMENT"
                            Command="{Binding SaveCommentCommand}"
                            FontSize="16"/>
                        <Button
                            IsVisible="{Binding IsEditCommentButtonVisible}"
                            Style="{StaticResource PrimaryButton}"
                            Text="EDIT COMMENT"
                            Command="{Binding EditCommentCommand}"
                            FontSize="16"/>
                    </VerticalStackLayout>
                </Grid>
            </components:TopRoundedBorderLayoutView>
        </ScrollView>
    </Grid>
</local:BaseContentPage>
