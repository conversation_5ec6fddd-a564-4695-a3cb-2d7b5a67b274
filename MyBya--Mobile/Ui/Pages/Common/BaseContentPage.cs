using System;
using CommunityToolkit.Maui.Behaviors;
using Microsoft.Maui.Controls.PlatformConfiguration;
using Microsoft.Maui.Controls.PlatformConfiguration.iOSSpecific;
using MyBya.Ui.ViewModels.Common;
using Serilog;

namespace MyBya.Ui.Pages.Common;

public abstract class BaseContentPage<T> : ContentPage where T : ViewModelBase
{
    private readonly string NameOfView;
    protected bool pageIsInitialized = false;

    protected BaseContentPage()
    {
        NameOfView = GetType().ToString();
        Log.Logger.Information("Page: " + NameOfView);

        Microsoft.Maui.Controls.NavigationPage.SetBackButtonTitle(this, "");
        Microsoft.Maui.Controls.NavigationPage.SetHasBackButton(this, false);
        Microsoft.Maui.Controls.NavigationPage.SetHasNavigationBar(this, false);

        SetBackGroundColor();
        SetStatusBarStyle();

        if (DeviceInfo.Platform == DevicePlatform.iOS)
        {
            var safeInsets = On<iOS>().SafeAreaInsets();
            this.Padding = safeInsets;
        }
        else
        {
            this.Padding = new Thickness(0, 0, 0, 0);
        }
    }

    protected virtual void SetStatusBarStyle()
    {
        try
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                var statusBarBehavior = new StatusBarBehavior();
                statusBarBehavior.StatusBarStyle = CommunityToolkit.Maui.Core.StatusBarStyle.LightContent;
                statusBarBehavior.StatusBarColor = Color.FromArgb("#352859");
                Behaviors.Add(statusBarBehavior);
            });
        }
        catch (Exception ex)
        {
            Log.Logger.Error("Error constructing base content page: " + ex.ToString());
        }
    }

    protected virtual void SetBackGroundColor()
    {
        BackgroundColor = Color.FromArgb("#352859");

        var gradientBrush = new LinearGradientBrush
        {
            GradientStops = new GradientStopCollection
            {
                new GradientStop { Color = Color.FromArgb("#483778"), Offset = 0.40f },
                new GradientStop { Color = Color.FromArgb("#231B3B"), Offset = 1.0f }
            }
        };

        Background = gradientBrush;
    }

    protected override async void OnAppearing()
    {
        try
        {
            if (!pageIsInitialized)
            {
                if (BindingContext != null)
                {
                    pageIsInitialized = true;
                    await ((ViewModelBase)BindingContext).OnAppearing();
                }
            }
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex.Message, ex);
            ((ViewModelBase)BindingContext).IsBusy = false;
            await ((ViewModelBase)BindingContext).ShowMessageError();
        }

        base.OnAppearing();
    }
}
