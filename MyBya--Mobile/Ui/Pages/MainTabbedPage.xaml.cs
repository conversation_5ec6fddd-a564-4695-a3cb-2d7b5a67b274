using MyBya.Ui.ViewModels;
using MyBya.Ui.ViewModels.TestProtocol;
using MyBya.Ui.Pages.TestProtocol;
using MyBya.Helpers;

namespace MyBya.Ui.Pages;

public partial class MainTabbedPage : TabbedPage
{
    public MainTabbedPage()
    {
        InitializeComponent();
        ModifyTabbedPageHandler();

        Children.Add(new NavigationPage(new HomePage(new HomeViewModel())) { Title = "Home", IconImageSource = "house.png" });
        Children.Add(new NavigationPage(new TestProtocolInitialCreationPage(new TestProtocolInitialCreationViewModel())) { Title = "Test", IconImageSource = "runner.png" });
        Children.Add(new NavigationPage(new TrainingSchedulePage(new TrainingScheduleViewModel())) { Title = "Calendar", IconImageSource = "ic_calendar.png" });
        Children.Add(new NavigationPage(new ComparePowerScoresSelectPage(new ComparePowerScoresSelectViewModel())) { Title = "History", IconImageSource = "bar_chart.png" });
        Children.Add(new NavigationPage(new AccountPage(new AccountViewModel())) { Title = "Account", IconImageSource = "user.png" });
    }

    private void ModifyTabbedPageHandler()
    {
        Microsoft.Maui.Handlers.TabbedViewHandler.Mapper.AppendToMapping("FixMultiTab", (handler, view) =>
        {
#if ANDROID
            if (handler?.PlatformView is AndroidX.ViewPager2.Widget.ViewPager2 viewPager)
            {
                viewPager.OffscreenPageLimit = 5;
            }
#endif
        });
    }
}
