<?xml version="1.0" encoding="utf-8" ?>
<local:BaseContentPage
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="MyBya.Ui.Pages.AthleteProfilePage"
    xmlns:local="clr-namespace:MyBya.Ui.Pages.Common"
    xmlns:viewModels="clr-namespace:MyBya.Ui.ViewModels"
    xmlns:views="clr-namespace:MyBya.Ui.Views"
    x:TypeArguments="viewModels:AthleteProfileViewModel"
    xmlns:components="clr-namespace:MyBya.Ui.Views.Components">
    <Grid
        RowDefinitions="*">
        <views:BaseView
            HasNavigationBack="True"
            TitleText="About Me"
            SubTitleText="Here you can modify your personal information used by the MyBYA application.">
            <views:BaseView.MainContent>
                <Grid
                    RowDefinitions="*"
                    ColumnDefinitions="*">
                    <components:TopRoundedBorderLayoutView
                        HorizontalOptions="Fill"
                        VerticalOptions="Fill"
                        Margin="0,0,0,0">
                        <VerticalStackLayout
                            Spacing="20"
                            Padding="10">
                            <components:LabelEntry
                                UseLeftPadding="True"
                                IsVisible="{Binding IsFirstTimeSetup}"
                                TitleFontSize="16"
                                EntryHeightRequest="40"
                                EntryHorizontalOptions="Fill"
                                BorderColor="Black"
                                TitleText="Email Address"
                                Keyboard="Email"
                                EntryText="{Binding Email,Mode=TwoWay}"/>
                            <components:LabelEntry
                                UseLeftPadding="True"
                                IsVisible="{Binding IsFirstTimeSetup}"
                                TitleFontSize="16"
                                EntryHeightRequest="40"
                                EntryHorizontalOptions="Fill"
                                BorderColor="Black"
                                TitleText="Password"
                                EntryIsPassword="True"
                                EntryText="{Binding Password,Mode=TwoWay}"/>
                            <components:LabelEntry
                                UseLeftPadding="True"
                                IsVisible="{Binding IsFirstTimeSetup}"
                                TitleFontSize="16"
                                EntryHeightRequest="40"
                                EntryHorizontalOptions="Fill"
                                BorderColor="Black"
                                TitleText="Confirm Password"
                                EntryIsPassword="True"
                                EntryText="{Binding ConfirmPassword,Mode=TwoWay}"/>
                            <Label
                                Text="Sports and Events"
                                FontSize="20"
                                TextColor="#D96CC6"/>
                            <components:LabelEntry
                                UseLeftPadding="True"
                                TitleFontSize="16"
                                EntryHeightRequest="40"
                                EntryHorizontalOptions="Fill"
                                BorderColor="Black"
                                TitleText="First Name"
                                EntryText="{Binding FirstName,Mode=TwoWay}"/>
                            <components:LabelEntry
                                UseLeftPadding="True"
                                TitleFontSize="16"
                                EntryHeightRequest="40"
                                EntryHorizontalOptions="Fill"
                                BorderColor="Black"
                                TitleText="Last Name"
                                EntryText="{Binding LastName,Mode=TwoWay}"/>
                            <components:SportsSelector
                                TitleText="Primary Sport"
                                TitleFontSize="16"
                                SelectorHorizontalOptions="Fill"
                                SelectedItem="{Binding SelectedSport}"/>
                            <components:LevelsSelector
                                TitleText="Level"
                                TitleFontSize="16"
                                SelectorHorizontalOptions="Fill"
                                SelectedItem="{Binding SelectedLevel}"/>
                            <BoxView
                                HeightRequest="1"
                                HorizontalOptions="Fill"
                                BackgroundColor="Black"/>
                            <VerticalStackLayout
                                IsVisible="{Binding ShowMyAccountSettings}">
                                <Label
                                    Margin="0,0,0,20"
                                    Text="My Account Settings"
                                    FontSize="20"
                                    TextColor="#D96CC6"/>
                                <Button
                                    Style="{StaticResource SecondaryButton}"
                                    Margin="0,0,0,20"
                                    Text="PASSWORD RESET"
                                    Command="{Binding PasswordResetCommand}"/>
                                <Button
                                    Command="{Binding DeleteAccountCommand}"
                                    Style="{StaticResource SecondaryButton}"
                                    Margin="0,0,0,20"
                                    Text="DELETE ACCOUNT"/>
                                <BoxView
                                    HeightRequest="1"
                                    HorizontalOptions="Fill"
                                    BackgroundColor="Black"/>
                            </VerticalStackLayout>
                            <Button
                                Style="{StaticResource PrimaryButton}"
                                Text="SAVE"
                                WidthRequest="200"
                                HorizontalOptions="Center"
                                Command="{Binding SaveCommand}"/>
                        </VerticalStackLayout>
                    </components:TopRoundedBorderLayoutView>
                </Grid>
            </views:BaseView.MainContent>
        </views:BaseView>
    </Grid>
</local:BaseContentPage>
