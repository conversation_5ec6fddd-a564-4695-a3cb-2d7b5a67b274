using MyBya.Models;
using MyBya.Ui.Pages.Common;
using MyBya.Ui.ViewModels;
using Serilog;
using Syncfusion.Maui.Toolkit.Calendar;

namespace MyBya.Ui.Pages;

public partial class TrainingSchedulePage : BaseContentPage<TrainingScheduleViewModel>
{
    public TrainingSchedulePage(TrainingScheduleViewModel viewModel)
    {
        InitializeComponent();
        BindingContext = viewModel;
    }

    private void ImageButton_Clicked(object sender, EventArgs e)
    {
        calendar.Backward();
    }

    private void ImageButton_Clicked1(object sender, EventArgs e)
    {
        calendar.Forward();
    }
}
