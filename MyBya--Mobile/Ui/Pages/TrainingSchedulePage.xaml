﻿<?xml version="1.0" encoding="utf-8" ?>
<local:BaseContentPage
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="MyBya.Ui.Pages.TrainingSchedulePage"
    xmlns:local="clr-namespace:MyBya.Ui.Pages.Common"
    xmlns:viewModels="clr-namespace:MyBya.Ui.ViewModels"
    xmlns:views="clr-namespace:MyBya.Ui.Views"
    x:TypeArguments="viewModels:TrainingScheduleViewModel"
    xmlns:components="clr-namespace:MyBya.Ui.Views.Components"
    xmlns:calendar="clr-namespace:Syncfusion.Maui.Toolkit.Calendar;assembly=Syncfusion.Maui.Toolkit"
    Title="TrainingSchedulePage">
    <ScrollView>
        <Grid
            RowDefinitions="Auto, Auto, *">
            <views:BaseView
                TitleText="Training Schedule"/>
            <VerticalStackLayout
                Padding="5"
                BackgroundColor="White"
                Grid.Row="1">
                <Grid
                    Margin="0,10,0,0"
                    RowDefinitions="*"
                    ColumnDefinitions="*">
                    <calendar:SfCalendar
                        SelectionMode="Single"
                        SelectionShape="Circle"
                        SelectionChangedCommand="{Binding CalendarSelectionChangedCommand}"
                        SelectedDate="{Binding SelectedDate, Mode=TwoWay}"
                        BackgroundColor="White"
                        x:Name="calendar"
                        View="Month">
                        <calendar:SfCalendar.HeaderView>
                            <calendar:CalendarHeaderView
                                Height="70"
                                Background="#483778"
                                ShowNavigationArrows="True">
                                <calendar:CalendarHeaderView.TextStyle>
                                    <calendar:CalendarTextStyle
                                        TextColor="White"
                                        FontSize="16" />
                                </calendar:CalendarHeaderView.TextStyle>
                            </calendar:CalendarHeaderView>
                        </calendar:SfCalendar.HeaderView>
                    </calendar:SfCalendar>
                </Grid>
            </VerticalStackLayout>
            <components:TopRoundedBorderLayoutView
                Margin="10,20,10,10"
                Grid.Row="2">
                <VerticalStackLayout Padding="20,0,20,20" Spacing="10">
                    <Label
                        Margin="5,0,0,10"
                        Text="{Binding WeekDayRange}"
                        TextColor="#D96CC6"
                        FontSize="22"
                        FontAttributes="Bold"
                        HorizontalOptions="Start"
                        VerticalTextAlignment="Start" />
                    <CollectionView
                        x:Name="collectionView"
                        ItemsSource="{Binding Items}">
                        <CollectionView.ItemTemplate>
                            <DataTemplate>
                                <StackLayout
                                    Margin="0,0,0,0">
                                    <VisualStateManager.VisualStateGroups>
                                        <VisualStateGroup Name="CommonStates">
                                            <VisualState Name="Normal"></VisualState>
                                            <VisualState Name="Selected">
                                                <VisualState.Setters>
                                                    <Setter Property="BackgroundColor" Value="Transparent"></Setter>
                                                </VisualState.Setters>
                                            </VisualState>
                                        </VisualStateGroup>
                                    </VisualStateManager.VisualStateGroups>
                                    <Frame
                                        BorderColor="{Binding IsSelected, Converter={StaticResource boolToBorderColorConverter}, Mode=TwoWay}"
                                        BackgroundColor="{Binding CardBackgroundColor}"
                                        Padding="10"
                                        CornerRadius="10">
                                        <Frame.GestureRecognizers>
                                            <TapGestureRecognizer
                                                Command="{Binding Source={x:Reference collectionView}, Path=BindingContext.SelectDayCommand}"
                                                CommandParameter="{Binding .}" />
                                        </Frame.GestureRecognizers>
                                        <Grid
                                            RowSpacing="10"
                                            RowDefinitions="Auto, Auto"
                                            ColumnDefinitions="60, 50, *">
                                            <Grid
                                                RowDefinitions="Auto, Auto"
                                                Padding="5"
                                                VerticalOptions="Center"
                                                Grid.RowSpan="2">
                                                <Label
                                                    VerticalTextAlignment="Start"
                                                    Text="{Binding Date, Converter={StaticResource DateTimeToDayNameConverter}}"
                                                    FontAttributes="Bold"
                                                    FontSize="16" />
                                                <Label
                                                    HorizontalTextAlignment="Start"
                                                    Grid.Row="1"
                                                    Text="{Binding Date, Converter={StaticResource DateTimeToDayConverter}}"
                                                    Margin="3,0,0,0"
                                                    FontSize="16" />
                                            </Grid>
                                            <Image
                                                Margin="0,0,20,0"
                                                Source="{Binding Date, Converter={StaticResource dateTimeToIconConverter}}"
                                                Scale="1.5"
                                                HeightRequest="20"
                                                WidthRequest="20"
                                                HorizontalOptions="Start"
                                                Grid.RowSpan="2"
                                                Grid.Column="1" />
                                            <StackLayout
                                                Margin="0,15,0,0"
                                                Spacing="10"
                                                BindableLayout.ItemsSource="{Binding Workouts}"
                                                Grid.Column="2">
                                                <BindableLayout.ItemTemplate>
                                                    <DataTemplate>
                                                        <Label
                                                            VerticalTextAlignment="Center"
                                                            HorizontalTextAlignment="Start"
                                                            Text="{Binding WorkoutName}"
                                                            FontSize="14"
                                                            FontAttributes="Bold"
                                                            FontFamily="Arial"
                                                            TextColor="Black" />
                                                    </DataTemplate>
                                                </BindableLayout.ItemTemplate>
                                            </StackLayout>
                                        </Grid>
                                    </Frame>
                                </StackLayout>
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                    </CollectionView>
                </VerticalStackLayout>
            </components:TopRoundedBorderLayoutView>
            <!-- <Button
                Style="{StaticResource DefaultButton}"
                Margin="0,0,0,20"
                x:Name="btnAddTraining"
                Text="Continue"
                Command="{Binding ContinueCommand}"
                CommandParameter="{Binding Source={x:Reference selector}, Path=SelectedItem}"
                HorizontalOptions="Center"
                VerticalOptions="End" /> -->
        </Grid>
    </ScrollView>
</local:BaseContentPage>
