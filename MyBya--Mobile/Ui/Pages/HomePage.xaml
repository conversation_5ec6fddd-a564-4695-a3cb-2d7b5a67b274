<?xml version="1.0" encoding="utf-8" ?>
<pages:BaseContentPage
    x:Class="MyBya.Ui.Pages.HomePage"
    x:TypeArguments="viewmodels:HomeViewModel"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:pages="clr-namespace:MyBya.Ui.Pages.Common"
    xmlns:viewmodels="clr-namespace:MyBya.Ui.ViewModels"
    Title="Home">

    <ScrollView>
        <StackLayout Padding="20" Spacing="20">

            <!-- Welcome Section -->
            <Frame BackgroundColor="#483778" CornerRadius="10" Padding="20">
                <StackLayout>
                    <Label
                        Text="{Binding WelcomeMessage}"
                        FontSize="24"
                        FontAttributes="Bold"
                        TextColor="#FF7FE8"
                        HorizontalOptions="Center" />

                    <Label
                        Text="We provide comprehensive, data-driven insights to create tailored training regimens that maximize efficiency, improve outcomes and transform athletic potential."
                        FontSize="16"
                        TextColor="White"
                        HorizontalOptions="Center"
                        Margin="0,10,0,0" />
                </StackLayout>
            </Frame>

            <Frame BackgroundColor="White"
                    CornerRadius="10"
                    Padding="20">
                <StackLayout>
                    <Label
                        Text="Training Plans Tailored to Your Body"
                        FontFamily="Spinnaker"
                        FontSize="20"
                        TextColor="#D96CC6"
                        FontAttributes="None"
                        HorizontalOptions="Center"/>
                    <Label
                        Text="Testing and analysis to identify untapped power and inform your tailored workout plan."
                        FontSize="16"
                        TextColor="#666"
                        HorizontalOptions="Center"
                        Margin="0,10,0,0"/>
                </StackLayout>
            </Frame>

            <!-- Quick Actions Section -->
            <Label
                Text="Quick Actions"
                FontSize="20"
                FontAttributes="Bold"
                TextColor="White"
                Margin="0,10,0,0" />

            <Grid ColumnDefinitions="*,*" RowDefinitions="Auto,Auto" ColumnSpacing="10" RowSpacing="10">

                <!-- Start Test Protocol -->
                <Frame Grid.Row="0" Grid.Column="0" BackgroundColor="White" CornerRadius="10" Padding="15">
                    <StackLayout>
                        <Label
                            Text="🏃‍♂️"
                            FontSize="30"
                            HorizontalOptions="Center" />
                        <Label
                            Text="Start Test"
                            FontSize="14"
                            FontAttributes="Bold"
                            TextColor="#352859"
                            HorizontalOptions="Center" />
                        <Label
                            Text="Protocol"
                            FontSize="14"
                            FontAttributes="Bold"
                            TextColor="#352859"
                            HorizontalOptions="Center" />
                    </StackLayout>
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding NavigateToTestProtocolCommand}" />
                    </Frame.GestureRecognizers>
                </Frame>

                <!-- View Calendar -->
                <Frame Grid.Row="0" Grid.Column="1" BackgroundColor="White" CornerRadius="10" Padding="15">
                    <StackLayout>
                        <Label
                            Text="📅"
                            FontSize="30"
                            HorizontalOptions="Center" />
                        <Label
                            Text="View"
                            FontSize="14"
                            FontAttributes="Bold"
                            TextColor="#352859"
                            HorizontalOptions="Center" />
                        <Label
                            Text="Calendar"
                            FontSize="14"
                            FontAttributes="Bold"
                            TextColor="#352859"
                            HorizontalOptions="Center" />
                    </StackLayout>
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding NavigateToCalendarCommand}" />
                    </Frame.GestureRecognizers>
                </Frame>

                <!-- View History -->
                <Frame Grid.Row="1" Grid.Column="0" BackgroundColor="White" CornerRadius="10" Padding="15">
                    <StackLayout>
                        <Label
                            Text="📊"
                            FontSize="30"
                            HorizontalOptions="Center" />
                        <Label
                            Text="View"
                            FontSize="14"
                            FontAttributes="Bold"
                            TextColor="#352859"
                            HorizontalOptions="Center" />
                        <Label
                            Text="History"
                            FontSize="14"
                            FontAttributes="Bold"
                            TextColor="#352859"
                            HorizontalOptions="Center" />
                    </StackLayout>
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding NavigateToHistoryCommand}" />
                    </Frame.GestureRecognizers>
                </Frame>

                <!-- Account Settings -->
                <Frame Grid.Row="1" Grid.Column="1" BackgroundColor="White" CornerRadius="10" Padding="15">
                    <StackLayout>
                        <Label
                            Text="👤"
                            FontSize="30"
                            HorizontalOptions="Center" />
                        <Label
                            Text="Account"
                            FontSize="14"
                            FontAttributes="Bold"
                            TextColor="#352859"
                            HorizontalOptions="Center" />
                        <Label
                            Text="Settings"
                            FontSize="14"
                            FontAttributes="Bold"
                            TextColor="#352859"
                            HorizontalOptions="Center" />
                    </StackLayout>
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding NavigateToAccountCommand}" />
                    </Frame.GestureRecognizers>
                </Frame>

            </Grid>

        </StackLayout>
    </ScrollView>

</pages:BaseContentPage>
