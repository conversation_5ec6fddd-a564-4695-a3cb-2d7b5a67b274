<?xml version="1.0" encoding="utf-8" ?>
<local:BaseContentPage
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="MyBya.Ui.Pages.ComparePowerScoresSelectPage"
    xmlns:local="clr-namespace:MyBya.Ui.Pages.Common"
    xmlns:viewModels="clr-namespace:MyBya.Ui.ViewModels"
    xmlns:views="clr-namespace:MyBya.Ui.Views"
    xmlns:components="clr-namespace:MyBya.Ui.Views.Components"
    x:TypeArguments="viewModels:ComparePowerScoresSelectViewModel"
    Title="ComparePowerScoresSelectPage">
    <Grid RowDefinitions="*">
        <views:BaseView
            TitleText="My Power Scores"
            SubTitleText="The precise, data-driven insights into energy system utilization enable us to optimize your training to enhance performance and achieve measurable results.">
            <views:BaseView.MainContent>
                <components:TopRoundedBorderLayoutView
                    TitleText="BioEnergetic Power Scores (BEPS)">
                    <VerticalStackLayout
                        Spacing="20"
                        Padding="5,5,5,20">
                        <components:SportsSelector
                            SelectedItem="{Binding SelectedSport}"
                            Margin="0,10,0,0"
                            SelectorHorizontalOptions="Fill"
                            TitleText="Sport"/>
                        <Button
                            IsEnabled="{Binding IsShowScoresButtonEnabled}"
                            FontSize="18"
                            WidthRequest="250"
                            HorizontalOptions="Center"
                            Text="SHOW SCORES"
                            Style="{StaticResource PrimaryButton}"
                            Command="{Binding ShowScoresCommand}"
                            IsVisible="{Binding IsAddTestButtonVisible}"/>
                    </VerticalStackLayout>
                </components:TopRoundedBorderLayoutView>
            </views:BaseView.MainContent>
        </views:BaseView>
    </Grid>
</local:BaseContentPage>
