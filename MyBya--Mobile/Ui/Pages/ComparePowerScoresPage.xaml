<?xml version="1.0" encoding="utf-8" ?>
<local:BaseContentPage
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="MyBya.Ui.Pages.ComparePowerScoresPage"
    xmlns:local="clr-namespace:MyBya.Ui.Pages.Common"
    xmlns:viewModels="clr-namespace:MyBya.Ui.ViewModels"
    xmlns:views="clr-namespace:MyBya.Ui.Views"
    x:TypeArguments="viewModels:ComparePowerScoresViewModel"
    xmlns:chart="clr-namespace:Syncfusion.Maui.Toolkit.Charts;assembly=Syncfusion.Maui.Toolkit"
    xmlns:components="clr-namespace:MyBya.Ui.Views.Components">
    <Grid
        RowDefinitions="*">
        <views:BaseView
            HasNavigationBack="True"
            TitleText="My Power Scores"
            SubTitleText="The precise, data-driven insights into energy system utilization enable us to optimize your training to enhance performance and achieve measurable results.">
            <views:BaseView.MainContent>
                <components:TopRoundedBorderLayoutView
                    TitleText="BioEnergetic Power Scores (BEPS)">
                    <VerticalStackLayout
                        Padding="20">
                        <Border
                            Padding="5"
                            Stroke="Black">
                            <chart:SfCartesianChart
                                HeightRequest="300">
                                <chart:SfCartesianChart.XAxes>
                                    <chart:CategoryAxis
                                        LabelPlacement="BetweenTicks"
                                        EdgeLabelsDrawingMode="Center"
                                        LabelsIntersectAction="MultipleRows">
                                        <chart:CategoryAxis.LabelStyle>
                                            <chart:ChartAxisLabelStyle FontSize="7"/>
                                        </chart:CategoryAxis.LabelStyle>
                                    </chart:CategoryAxis>
                                </chart:SfCartesianChart.XAxes>

                                <chart:SfCartesianChart.YAxes>
                                    <chart:NumericalAxis ShowMajorGridLines="True" />
                                </chart:SfCartesianChart.YAxes>

                                <!-- Séries -->
                                <chart:SfCartesianChart.Series>

                                    <!-- Suas três séries de valores reais -->
                                    <chart:ColumnSeries
                                        ItemsSource="{Binding ChartItemsTest1}"
                                        XBindingPath="Name"
                                        YBindingPath="Value"
                                        Fill="{Binding BarColorTest1}"
                                        Spacing="0.2"
                                        Width="1"/>

                                    <chart:ColumnSeries
                                        ItemsSource="{Binding ChartItemsTest2}"
                                        XBindingPath="Name"
                                        YBindingPath="Value"
                                        Fill="{Binding BarColorTest2}"
                                        Spacing="0.2"
                                        Width="1"/>

                                    <chart:ColumnSeries
                                        ItemsSource="{Binding ChartItemsTest3}"
                                        XBindingPath="Name"
                                        YBindingPath="Value"
                                        Fill="{Binding BarColorTest3}"
                                        Width="1"
                                        Spacing="0.2"/>

                                     <chart:ColumnSeries
                                        ItemsSource="{Binding ChartItemsTest4}"
                                        XBindingPath="Name"
                                        YBindingPath="Value"
                                        Fill="{Binding BarColorTest4}"
                                        Width="1"
                                        Spacing="0.2"/>

                                </chart:SfCartesianChart.Series>
                            </chart:SfCartesianChart>
                        </Border>
                        <CollectionView
                            x:Name="collectionView"
                            Margin="0,20,0,0"
                            ItemsSource="{Binding Items}">
                            <CollectionView.ItemTemplate>
                                <DataTemplate>
                                    <Grid
                                        RowSpacing="20"
                                        Margin="0,10,0,0"
                                        RowDefinitions="Auto,Auto,Auto"
                                        ColumnDefinitions="25,Auto,Auto,Auto, Auto,*">
                                        <BoxView
                                            Color="{Binding BoxColor}"
                                            HorizontalOptions="Start"
                                            WidthRequest="10"
                                            Grid.RowSpan="4"/>
                                        <Label
                                            Grid.Column="1"
                                            Grid.ColumnSpan="4"
                                            FontFamily="RobotoBold"
                                            HorizontalTextAlignment="Start"
                                            Text="{Binding Date}"
                                            TextColor="Black"
                                            FontAttributes="Bold"
                                            FontSize="18"/>
                                        <!--!> First row -->
                                        <components:LabelValue
                                            Grid.Row="1"
                                            Grid.Column="1"
                                            HorizontalOptions="Center"
                                            TitleText="AF"
                                            TitleTextColor="#483778"
                                            LableValueText="{Binding Af}"
                                            TitleFontSize="15"
                                            ValueFontSize="18"/>
                                        <components:LabelValue
                                            Grid.Row="1"
                                            Grid.Column="2"
                                            TitleText="PAC"
                                            TitleTextColor="#483778"
                                            HorizontalOptions="Center"
                                            LableValueText="{Binding Pac}"
                                            TitleFontSize="15"
                                            ValueFontSize="18"/>
                                        <components:LabelValue
                                            Grid.Row="1"
                                            Grid.Column="3"
                                            TitleText="LTCC"
                                            TitleTextColor="#483778"
                                            LableValueText="{Binding Ltcc}"
                                            TitleFontSize="15"
                                            ValueFontSize="18"/>
                                        <components:LabelValue
                                            Grid.Row="1"
                                            Grid.Column="4"
                                            TitleText="ARC-3"
                                            TitleTextColor="#483778"
                                            LableValueText="{Binding Arc3}"
                                            TitleFontSize="15"
                                            ValueFontSize="18"/>
                                        <!--!> Second row -->
                                        <components:LabelValue
                                            Grid.Column="1"
                                            Grid.Row="2"
                                            TitleText="ARC-2"
                                            TitleTextColor="#483778"
                                            TitleFontSize="15"
                                            ValueFontSize="18"
                                            HorizontalOptions="Center"
                                            LableValueText="{Binding Arc2}"/>
                                        <components:LabelValue
                                            Margin="20,0,0,0"
                                            Grid.Column="2"
                                            Grid.Row="2"
                                            TitleText="ARC-1"
                                            TitleTextColor="#483778"
                                            TitleFontSize="15"
                                            ValueFontSize="18"
                                            LableValueText="{Binding Arc1}"/>
                                        <components:LabelValue
                                            Margin="20,0,0,0"
                                            Grid.Row="3"
                                            Grid.Column="3"
                                            TitleText="ANRC-2"
                                            TitleTextColor="#483778"
                                            TitleFontSize="15"
                                            ValueFontSize="18"
                                            LableValueText="{Binding Anrc2}"/>
                                        <components:LabelValue
                                            Margin="20,0,0,0"
                                            TitleTextColor="#483778"
                                            Grid.Row="2"
                                            Grid.Column="4"
                                            TitleText="ANRC-1"
                                            TitleFontSize="15"
                                            ValueFontSize="18"
                                            LableValueText="{Binding Anrc1}"/>
                                        <ImageButton
                                            CommandParameter="{Binding .}"
                                            Command="{Binding Source={RelativeSource AncestorType={x:Type viewModels:ComparePowerScoresViewModel}}, Path=DeleteTestCommand}"
                                            IsVisible="{Binding IsDeleteButtonVisible}"
                                            HorizontalOptions="End"
                                            VerticalOptions="Center"
                                            Grid.RowSpan="5"
                                            Source="ic_delete.png"
                                            Grid.Column="5">
                                            <ImageButton.Margin>
                                                <OnPlatform x:TypeArguments="Thickness">
                                                    <On Platform="iOS" Value="0,0,-10,0"/>
                                                    <On Platform="Android" Value="0,0,0,0"/>
                                                </OnPlatform>
                                            </ImageButton.Margin>
                                            <ImageButton.HeightRequest> 
                                                <OnPlatform x:TypeArguments="x:Double">
                                                    <On Platform="iOS" Value="20"/>
                                                    <On Platform="Android" Value="35"/>
                                                </OnPlatform>
                                            </ImageButton.HeightRequest>
                                            <ImageButton.WidthRequest>
                                                <OnPlatform x:TypeArguments="x:Double">
                                                    <On Platform="iOS" Value="20"/>
                                                    <On Platform="Android" Value="35"/>
                                                </OnPlatform>
                                            </ImageButton.WidthRequest>
                                        </ImageButton>
                                    </Grid>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>
                        <Button
                            Style="{StaticResource PrimaryButton}"
                            Margin="0,20,0,0"
                            Text="ADD TEST TO COMPARE"
                            IsVisible="{Binding IsAddTestButtonVisible}"
                            Command="{Binding AddTestCommand}"/>
                    </VerticalStackLayout>
                </components:TopRoundedBorderLayoutView>
            </views:BaseView.MainContent>
        </views:BaseView>
    </Grid>
</local:BaseContentPage>
