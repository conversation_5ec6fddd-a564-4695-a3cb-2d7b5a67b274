<?xml version="1.0" encoding="utf-8" ?>
<pages:BaseContentPage
    x:Class="MyBya.Ui.Pages.AccountPage"
    x:TypeArguments="viewmodels:AccountViewModel"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:pages="clr-namespace:MyBya.Ui.Pages.Common"
    xmlns:views="clr-namespace:MyBya.Ui.Views"
    xmlns:viewmodels="clr-namespace:MyBya.Ui.ViewModels"
    Title="Account">
    <Grid
        RowDefinitions="*">
        <ScrollView>
            <VerticalStackLayout
                Margin="0,20,0,0"
                VerticalOptions="Start"
                Spacing="20">
                <Label
                    Text="User Profile"
                    FontSize="24"
                    TextColor="#FF7FE8"
                    HorizontalOptions="Center" />
                <Image
                    Source="ic_user_profile.png"
                    WidthRequest="110"
                    HeightRequest="110"
                    HorizontalOptions="Center"
                    VerticalOptions="Center" />
                <Label
                    Text="{Binding UserName}"
                    FontSize="18"
                    TextColor="White"
                    HorizontalOptions="Center" />
                <Label
                    Text="{Binding UserEmail}"
                    TextDecorations="Underline"
                    FontSize="15"
                    TextColor="#FFF"
                    HorizontalOptions="Center"

                    Margin="0,0,0,12"/>
                <Button
                    Command="{Binding EditProfileCommand}"
                    Text="EDIT MY INFO"
                    Style="{StaticResource ProfileButtonStyle}" />
                <Button
                    Command="{Binding MyTrainingPlansCommand}"
                    Text="MY TRAINING PLANS"
                    Style="{StaticResource ProfileButtonStyle}" />
                <Button
                    Text="WORKOUT HISTORY"
                    Command="{Binding WorkoutHistoryCommand}"
                    Style="{StaticResource ProfileButtonStyle}" />
                <Button
                    Command="{Binding MyTestsCommand}"
                    Text="MY TESTS"
                    Style="{StaticResource ProfileButtonStyle}" />
                <Button
                    Text="TERRA AUTHORIZATION"
                    Style="{StaticResource ProfileButtonStyle}"
                    Command="{Binding TerraAuthCommand}"/>
                <Label
                    Text="PRIVACY POLICY"
                    FontSize="14"
                    TextColor="White"
                    HorizontalOptions="Center"
                    Margin="0,10,0,0" />
                <Button
                    Margin="0,0,0,25"
                    Text="SIGN OUT"
                    TextColor="#FF7FE8"
                    BorderColor="#FF7FE8"
                    BackgroundColor="Transparent"
                    CornerRadius="5"
                    HorizontalOptions="Center"
                    Style="{StaticResource ProfileButtonStyle}"
                    HeightRequest="40"
                    WidthRequest="200"
                    Command="{Binding LogoutCommand}"/>
            </VerticalStackLayout>
        </ScrollView>
    </Grid>
</pages:BaseContentPage>
