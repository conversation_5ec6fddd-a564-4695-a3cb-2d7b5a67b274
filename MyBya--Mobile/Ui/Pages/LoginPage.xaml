<?xml version="1.0" encoding="utf-8" ?>
<local:BaseContentPage
    x:Class="MyBya.Ui.Pages.LoginPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:viewmodels="clr-namespace:MyBya.Ui.ViewModels"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns:inputLayout="clr-namespace:Syncfusion.Maui.Toolkit.TextInputLayout;assembly=Syncfusion.Maui.Toolkit"
    x:TypeArguments="viewmodels:LoginViewModel"
    xmlns:local="clr-namespace:MyBya.Ui.Pages.Common"
    BackgroundColor="{DynamicResource PageBackgroundColor}">

    <ScrollView>
        <Grid
            RowDefinitions="*, Auto, *"
            Padding="32,0">
            <Frame
                Margin="0,20,0,0"
                BackgroundColor="White"
                CornerRadius="16"
                HasShadow="True"
                WidthRequest="221"
                HeightRequest="215"
                HorizontalOptions="Center"
                VerticalOptions="Center"
                Padding="30">

                <!-- MyBya Logo Image -->
                <Image
                    Source="mybya_logo.png"
                    Aspect="AspectFit"
                    HorizontalOptions="Center"
                    VerticalOptions="Center" />
            </Frame>

            <StackLayout
                Margin="0,20,0,0"
                Grid.Row="1"
                Spacing="5"
                VerticalOptions="Center">
                <Label
                    Text="{Binding ErrorMessage}"
                    IsVisible="{Binding ErrorMessage, Converter={StaticResource IsNotNullOrEmptyConverter}}"
                    TextColor="{DynamicResource DangerColor}"
                    FontSize="14"
                    HorizontalTextAlignment="Center"
                    Margin="0,0,0,10" />
                <inputLayout:SfTextInputLayout
                    BackgroundColor="Transparent"
                    ContainerBackground="White"
                    HeightRequest="85"
                    Hint="Username">
                    <Entry
                        x:Name="UsernameEntry"
                        Text="{Binding Username}"
                        Keyboard="Email"
                        FontSize="16"
                        BackgroundColor="Transparent"
                        Margin="16,12"
                        ReturnType="Next"/>
                </inputLayout:SfTextInputLayout>
                <inputLayout:SfTextInputLayout
                    BackgroundColor="Transparent"
                    ContainerBackground="White"
                    HeightRequest="85"
                    EnablePasswordVisibilityToggle="true"
                    Hint="Password">
                    <Entry
                        x:Name="PasswordEntry"
                        Text="{Binding Password}"
                        FontSize="16"
                        BackgroundColor="Transparent"
                        Margin="16,12,48,12"
                        ReturnType="Done"/>
                </inputLayout:SfTextInputLayout>
                <Button
                    Text="Sign In"
                    Command="{Binding LoginCommand}"
                    BackgroundColor="#9C6ADE"
                    TextColor="White"
                    FontSize="16"
                    FontAttributes="Bold"
                    HeightRequest="50"
                    IsEnabled="{Binding IsLoading, Converter={StaticResource InvertedBoolConverter}}">
                    <Button.Shadow>
                        <Shadow
                            Brush="{DynamicResource Primary}"
                            Opacity="0.3"
                            Radius="8"
                            Offset="0,4" />
                    </Button.Shadow>
                </Button>

                <!-- Loading Indicator -->
                <ActivityIndicator IsVisible="{Binding IsLoading}"
                                   IsRunning="{Binding IsLoading}"
                                   Color="{DynamicResource Primary}"
                                   HeightRequest="30" />

                <!-- Forgot Password -->
                <Label
                    Text="Forgot Password?"
                    BackgroundColor="Transparent"
                    TextColor="White"
                    FontSize="16"
                    TextDecorations="Underline"
                    HorizontalOptions="Center">
                    <Label.GestureRecognizers>
                        <TapGestureRecognizer
                            Command="{Binding NavigateToForgotPasswordCommand}"
                            NumberOfTapsRequired="1" />
                    </Label.GestureRecognizers>
                </Label>

                <!-- Divider -->
                <Grid
                    ColumnDefinitions="*, Auto, *"
                    Margin="0,20,0,10">
                    <BoxView
                        Grid.Column="0"
                        BackgroundColor="White"
                        HeightRequest="1"
                        VerticalOptions="Center" />
                    <Label
                        Grid.Column="1"
                        Text="OR"
                        FontSize="12"
                        TextColor="White"
                        Margin="16,0" />
                    <BoxView
                        Grid.Column="2"
                        BackgroundColor="White"
                        HeightRequest="1"
                        VerticalOptions="Center" />
                </Grid>

                <!-- Sign Up Link -->
                <StackLayout
                    Orientation="Horizontal"
                    HorizontalOptions="Center"
                    VerticalOptions="Center"
                    Margin="0,32,0,0">
                    <Label
                        Text="Don't have an account? "
                        FontSize="18"
                        TextColor="White"
                        VerticalOptions="Center" />
                    <Label
                        Text="Sign Up"
                        BackgroundColor="Transparent"
                        TextDecorations="Underline"
                        TextColor="White"
                        FontSize="18"
                        FontAttributes="Bold"
                        Padding="0"
                        Margin="0"
                        HeightRequest="25"
                        VerticalOptions="Center">
                        <Label.GestureRecognizers>
                            <TapGestureRecognizer
                                Command="{Binding NavigateToSignUpCommand}"
                                NumberOfTapsRequired="1" />
                        </Label.GestureRecognizers>
                    </Label>
                </StackLayout>
            </StackLayout>
        </Grid>
    </ScrollView>
</local:BaseContentPage>
