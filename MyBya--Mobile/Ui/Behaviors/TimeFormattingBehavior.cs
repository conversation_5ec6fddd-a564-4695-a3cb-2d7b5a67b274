using Microsoft.Maui.Controls;
using System.Text.RegularExpressions;

namespace MyBya.Ui.Behaviors
{
    public class TimeFormattingBehavior : Behavior<Entry>
    {
        private bool _isFormatting;

        protected override void OnAttachedTo(Entry entry)
        {
            entry.TextChanged += OnEntryTextChanged;
            base.OnAttachedTo(entry);
        }

        protected override void OnDetachingFrom(Entry entry)
        {
            entry.TextChanged -= OnEntryTextChanged;
            base.OnDetachingFrom(entry);
        }

        private void OnEntryTextChanged(object? sender, TextChangedEventArgs e)
        {
            if (_isFormatting)
                return;

            var entry = sender as Entry;

            if (entry == null)
                return;

            string text = e.NewTextValue;

            var digits = Regex.Replace(text, "[^0-9]", "");

            if (digits.Length > 4)
                digits = digits.Substring(0, 4);

            string formatted = digits.Length switch
            {
                <= 2 => digits,
                <= 4 => $"{digits[..2]}:{digits[2..]}",
                _ => $"{digits[..2]}:{digits[2..4]}"
            };

            _isFormatting = true;
            entry.Text = formatted;
            _isFormatting = false;

#if ANDROID
        Microsoft.Maui.Controls.Application.Current?.Dispatcher.Dispatch(() =>
        {
            try
            {
                if (entry.Handler?.PlatformView is Android.Widget.EditText editText)
                {
                    if (!string.IsNullOrEmpty(editText.Text))
                        editText.SetSelection(editText.Text.Length);
                }
            }
            catch
            {

            }
        });
#endif
        }
    }
}
