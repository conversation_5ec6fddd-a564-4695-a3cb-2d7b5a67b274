
using MyBya.Helpers;
using MyBya.Interfaces;

namespace MyBya.Ui.Views;

public partial class BaseView : ContentView
{
	public static readonly BindableProperty MainContentProperty = BindableProperty.Create(
        nameof(MainContent),
        typeof(StackLayout),
        typeof(BaseView),
        default,
        BindingMode.TwoWay
    );

	public static readonly BindableProperty TitleTextProperty = BindableProperty.Create(
        nameof(TitleText),
        typeof(string),
        typeof(BaseView),
        default,
        BindingMode.TwoWay,
		propertyChanged: (bindableObject, oldValue, newValue) =>
		{
			var ctrl = (BaseView)bindableObject;
			ctrl.title.Text = (string)newValue;
		}
    );

	public static readonly BindableProperty SubTitleTextProperty = BindableProperty.Create(
        nameof(SubTitleText),
        typeof(string),
        typeof(BaseView),
        default,
        BindingMode.TwoWay,
		propertyChanged: (bindableObject, oldValue, newValue) =>
		{
			var ctrl = (BaseView)bindableObject;
			ctrl.subTitle.Text = (string)newValue;
		}
    );

	public string TitleText
	{
		get => (string)GetValue(TitleTextProperty);
		set => SetValue(TitleTextProperty, value);
	}

	public string SubTitleText
	{
		get => (string)GetValue(SubTitleTextProperty);
		set => SetValue(SubTitleTextProperty, value);
	}

    // Add this field above the property
    private bool _hasNavigationBack;

    public bool HasNavigationBack
    {
        get => _hasNavigationBack;
        set
        {
            _hasNavigationBack = value;
            navigationIcon.IsVisible = value;

            if (value)
            {
                if (DeviceInfo.Platform == DevicePlatform.iOS)
                {
                    title.Margin = new Thickness(0, 18, 23, 10);
                }
                else if (DeviceInfo.Platform == DevicePlatform.Android)
                {
                    title.Margin = new Thickness(0, 17, 23, 0);
                }
            }
        }
    }

	public StackLayout MainContent
	{
		get => (StackLayout)GetValue(MainContentProperty);
		set => SetValue(MainContentProperty, value);
	}

	public BaseView()
	{
		InitializeComponent();
		MainContent = new StackLayout();
		mainContent.Add(MainContent);
		HasNavigationBack = false;
    }

    async void navigationIcon_Clicked(object sender, EventArgs e)
    {
        await ServiceHelper
            .GetService<INavigationService>()
            .NavigateBack();
    }
}
