<?xml version="1.0" encoding="utf-8" ?>
<toolkit:Popup xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="MyBya.Ui.Views.Popups.SelectPreviousTestPopup"
             xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
             xmlns:components="clr-namespace:MyBya.Ui.Views.Components">
    <VerticalStackLayout
        BackgroundColor="#FFFFFF"
        Spacing="25"
        Padding="15"
        VerticalOptions="Center"
        HeightRequest="250"
        WidthRequest="350">
        <Label
            Text="Select Previous Test"
            HorizontalOptions="Center"
            VerticalOptions="Center"
            FontSize="20"
            TextColor="Black"/>
         <Grid
            VerticalOptions="Center"
            RowDefinitions="*"
            ColumnDefinitions="*">
            <Picker
                Margin="0,30,0,0"
                SelectedIndexChanged="Picker_SelectedIndexChanged"
                SelectedItem="{Binding SelectedPreviousTestDate}"
                ItemsSource="{Binding PreviousTestDates}"
                HorizontalOptions="Fill"/>
            <components:Selector
                TitleFontSize="17"
                SelectorHorizontalOptions="Fill"
                x:Name="selector"
                InputTransparent="True"
                SelectorBackgroundColor="White"
                TitleTextColor="Black"
                TitleText="Test"/>
        </Grid>

        <Grid
            ColumnDefinitions="*, *">
            <Button
                TextColor="#483778"
                x:Name="cancelButton"
                Margin="0,0,0,0"
                Text="CANCEL"
                FontSize="17"
                Clicked="cancelButton_Clicked"
                BackgroundColor="Transparent"
                VerticalOptions="End"/>
            <Button
                IsEnabled="{Binding IsSelectButtonEnabled}"
                Grid.Column="1"
                x:Name="selectButton"
                Style="{StaticResource SecondaryButton}"
                Margin="0,0,0,0"
                Text="SELECT"
                FontSize="17"
                Clicked="selectButton_Clicked"/>
        </Grid>
    </VerticalStackLayout>
</toolkit:Popup>
