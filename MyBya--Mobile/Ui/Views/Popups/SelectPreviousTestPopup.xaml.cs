using CommunityToolkit.Maui.Views;
using MyBya.Constants;
using MyBya.Enums;
using MyBya.Helpers;
using MyBya.Interfaces;
using MyBya.Models;
using MyBya.Ui.ViewModels;
using Serilog;

namespace MyBya.Ui.Views.Popups;

public partial class SelectPreviousTestPopup : Popup
{
    public Func<TestDateModel, Task>? Callback { get; set; }

    public SelectPreviousTestPopup(SportEnum sportEnum)
    {
        InitializeComponent();
        BindingContext = new SelectPreviousTestPopupViewModel(sportEnum);
    }

    public void AddTestDetailAthleteIdsToAvoid(List<int> testDetailAthleteIdsToAvoid)
    {
        if (testDetailAthleteIdsToAvoid == null)
            return;

        if (BindingContext is SelectPreviousTestPopupViewModel viewModel)
            viewModel.AddTestDetailAthleteIdsToAvoid(testDetailAthleteIdsToAvoid);
    }

    private async void cancelButton_Clicked(object sender, EventArgs e)
    {
        await CloseAsync();
    }

    private async void selectButton_Clicked(object sender, EventArgs e)
    {
        try
        {
            if (BindingContext is SelectPreviousTestPopupViewModel viewModel)
            {
                var testModel = viewModel.GetSelectedTestByDate(selector.SelectedItem?.ToString() ?? "");

                if (Callback != null)
                    await Callback(testModel ?? new TestDateModel());
            }
        }
        catch (Exception ex)
        {
            await ServiceHelper
                 .GetService<IAlertService>()
                 .ShowAlertAsync("MyBya", FriendlyUserTextsConstants.GERENERIC_ERROR);

            Log.Logger.Error(ex, ex.Message);
        }
        finally
        {
            await CloseAsync();
        }
    }

    private void Picker_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (BindingContext is not SelectPreviousTestPopupViewModel viewModel)
            return;

        viewModel.IsSelectButtonEnabled = true;

        if (sender is Picker picker && picker.SelectedItem != null)
            selector.SetSelectedItem(picker.SelectedItem);
    }
}
