using CommunityToolkit.Maui.Views;
using MyBya.Constants;
using MyBya.Helpers;
using MyBya.Interfaces;
using Serilog;

namespace MyBya.Ui.Views.Popups;

public partial class ConfirmationPopup : Popup
{
    public Func<bool, Task>? OnConfirmAsync { get; set; }

    public Action<bool>? OnConfirm { get; set; }

    public ConfirmationPopup()
    {
        InitializeComponent();
    }

    public void AddTitle(string title)
    {
        if (title != null)
            titleLabel.Text = title;
    }

    public void AddMessage(string message)
    {
        if (message != null)
            messageLabel.Text = message;
    }

    private async void cancelButton_Clicked(object sender, EventArgs e)
    {
        if (OnConfirmAsync != null)
            await OnConfirmAsync(false);
        else if (OnConfirm != null)
            OnConfirm(false);

        await CloseAsync();
    }

    private async void selectButton_Clicked(object sender, EventArgs e)
    {
        try
        {
            if (OnConfirmAsync != null)
                await OnConfirmAsync(true);
            else if (OnConfirm != null)
                OnConfirm(true);
        }
        catch (Exception ex)
        {
            await ServiceHelper
                .GetService<IAlertService>()
                .ShowAlertAsync("MyBya", FriendlyUserTextsConstants.GERENERIC_ERROR);

            Log.Logger.Error(ex, ex.Message);
        }
        finally
        {
            await CloseAsync();
        }
    }
}
