<?xml version="1.0" encoding="utf-8" ?>
<toolkit:Popup xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="MyBya.Ui.Views.Popups.ConfirmationPopup"
             xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit">
    <VerticalStackLayout
        BackgroundColor="#FFFFFF"
        Spacing="25"
        Padding="15"
        VerticalOptions="Center"
        HeightRequest="200"
        WidthRequest="350">
        <Label
            x:Name="titleLabel"
            HorizontalOptions="Center"
            VerticalOptions="Center"
            FontSize="20"
            TextColor="Black"/>
        <Label
            x:Name="messageLabel"
            HorizontalOptions="Center"
            VerticalOptions="Center"
            FontSize="16"
            TextColor="Black"/>
        <Grid
            ColumnDefinitions="*, *">
            <Button
                TextColor="#483778"
                x:Name="cancelButton"
                Margin="0,0,0,0"
                Text="CANCEL"
                FontSize="17"
                Clicked="cancelButton_Clicked"
                BackgroundColor="Transparent"
                VerticalOptions="End"/>
            <Button
                Grid.Column="1"
                x:Name="selectButton"
                Style="{StaticResource SecondaryButton}"
                Margin="0,0,0,0"
                Text="CONFIRM"
                Clicked="selectButton_Clicked"
                FontSize="17"/>
        </Grid>
    </VerticalStackLayout>
</toolkit:Popup>
