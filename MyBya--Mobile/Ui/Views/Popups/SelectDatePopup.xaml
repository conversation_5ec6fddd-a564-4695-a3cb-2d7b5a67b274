<?xml version="1.0" encoding="utf-8" ?>
<toolkit:Popup
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns:components="clr-namespace:MyBya.Ui.Views.Components"
    x:Class="MyBya.Ui.Views.Popups.SelectDatePopup">
    <VerticalStackLayout
        BackgroundColor="#352859"
        Spacing="25"
        Padding="20"
        VerticalOptions="Center"
        HeightRequest="200"
        WidthRequest="300">
         <Grid
            VerticalOptions="Center"
            RowDefinitions="*"
            ColumnDefinitions="*">
            <components:FormDatePicker
                Margin="0,20,0,0"
                VerticalOptions="Center"
                DateSelected="datePicker_DateSelected"
                x:Name="datePicker"/>
            <components:Selector
                SelectorHorizontalOptions="Fill"
                x:Name="selector"
                InputTransparent="True"
                SelectorBackgroundColor="White"
                TitleTextColor="White"
                TitleText="Select Training Plan Start"/>
        </Grid>
         <Button
            Style="{StaticResource DefaultButton}"
            Margin="0,0,0,20"
            x:Name="btnAddTraining"
            Text="Continue"
            Clicked="btnAddTraining_Clicked"
            CommandParameter="{Binding Source={x:Reference selector}, Path=SelectedItem}"
            VerticalOptions="End"/>
    </VerticalStackLayout>
</toolkit:Popup>
