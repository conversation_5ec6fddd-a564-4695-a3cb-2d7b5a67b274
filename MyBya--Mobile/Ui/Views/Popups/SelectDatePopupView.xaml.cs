using CommunityToolkit.Maui.Views;
using MyBya.Constants;
using MyBya.Helpers;
using MyBya.Interfaces;
using Serilog;

namespace MyBya.Ui.Views.Popups;

public partial class SelectDatePopup : Popup
{
	public Func<DateTime, Task>? callback;

	public SelectDatePopup()
	{
		InitializeComponent();
	}

	private void datePicker_DateSelected(object sender, DateChangedEventArgs e)
	{
		selector.SetSelectedItem(e.NewDate.ToString("D"));
    }

    private async void btnAddTraining_Clicked(object sender, EventArgs e)
    {
        try
        {
            if (callback != null)
                await callback(datePicker.Date);
        }
        catch (Exception ex)
        {
            await ServiceHelper
                .GetService<IAlertService>()
                .ShowAlertAsync("MyBya", FriendlyUserTextsConstants.GERENERIC_ERROR);

            Log.Logger.Error(ex, ex.Message);
        }
        finally
        {
            Close();
        }
	}
}
