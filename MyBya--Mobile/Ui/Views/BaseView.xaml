﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="MyBya.Ui.Views.BaseView"
             BackgroundColor="Transparent">
        <Grid
            RowDefinitions="Auto, *">
            <Image
                Scale="1.2"
                Source="test_protcol_rouding_header.png">
                <Image.Margin>
                    <OnPlatform x:TypeArguments="Thickness">
                        <On Platform="iOS" Value="0,-6,0,0"/>
                        <On Platform="Android" Value="0,0,0,0"/>
                    </OnPlatform>
                </Image.Margin>
            </Image>
            <ScrollView
                Grid.Row="1"
                Margin="0,0,0,0"
                BackgroundColor="Transparent">
                <Grid
                    RowDefinitions="Auto, Auto, *"
                    Margin="0,10,0,0">
                    <Grid
                        RowDefinitions="*"
                        ColumnDefinitions="Auto, *">
                        <ImageButton
                            Clicked="navigationIcon_Clicked"
                            Scale="0.7"
                            x:Name="navigationIcon"
                            IsVisible="False"
                            Margin="0,15,0,0"
                            HorizontalOptions="Start"
                            BackgroundColor="Transparent"
                            Source="ic_back.png">
                            <ImageButton.HeightRequest>
                                <OnPlatform x:TypeArguments="x:Double">
                                    <On Platform="iOS" Value="15"/>
                                    <On Platform="Android" Value="40"/>
                                </OnPlatform>
                            </ImageButton.HeightRequest>
                            <ImageButton.WidthRequest>
                                    <OnPlatform x:TypeArguments="x:Double">
                                        <On Platform="iOS" Value="15"/>
                                        <On Platform="Android" Value="40"/>
                                    </OnPlatform>
                            </ImageButton.WidthRequest>
                        </ImageButton>
                         <Label
                            Grid.Column="1"
                            x:Name="title"
                            FontSize="24"
                            TextColor="#FF7FE8"
                            HorizontalTextAlignment="Center">
                            <Label.Margin>
                                <OnPlatform x:TypeArguments="Thickness">
                                    <On Platform="iOS" Value="0,18,0,10"/>
                                    <On Platform="Android" Value="0,17,0,0"/>
                                </OnPlatform>
                            </Label.Margin>
                        </Label>
                    </Grid>
                    <Label
                        TextColor="White"
                        Grid.Row="1"
                        x:Name="subTitle"
                        Margin="10,10,10,10"
                        FontSize="18"
                        HorizontalTextAlignment="Start"/>
                    <Grid
                        BackgroundColor="Transparent"
                        RowDefinitions="*"
                        Margin="0,15,0,0"
                        Grid.Row="2"
                        Padding="10,0,10,0"
                        x:Name="mainContent">
                    </Grid>
                </Grid>
            </ScrollView>
        </Grid>
</ContentView>
