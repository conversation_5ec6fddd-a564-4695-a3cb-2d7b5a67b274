using System;

namespace MyBya.Ui.Views.Components;

public class CEntry : Entry
{
    public static readonly BindableProperty BorderColorProperty = BindableProperty.Create(
        nameof(BorderColor),
        typeof(Color),
        typeof(CEntry),
        default,
        BindingMode.TwoWay,
		propertyChanged: (bindableObject, oldValue, newValue) =>
		{
		}
    );

    public static readonly BindableProperty CornerRadiusProperty = BindableProperty.Create(
        nameof(CornerRadius),
        typeof(double),
        typeof(CEntry),
        default,
        BindingMode.TwoWay,
		propertyChanged: (bindableObject, oldValue, newValue) =>
		{
		}
    );

    public Color BorderColor
    {
        get => (Color)GetValue(BorderColorProperty);
        set => SetValue(BorderColorProperty, value);
    }

    public double CornerRadius
    {
        get => (double)GetValue(CornerRadiusProperty);
        set => SetValue(CornerRadiusProperty, value);
    }

    public bool UseLeftPadding { get; set; }

    public CEntry()
    {
        CornerRadius = 5;
    }
}
