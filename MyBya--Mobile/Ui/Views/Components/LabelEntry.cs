using System;
using MyBya.Interfaces;
using MyBya.Ui.Views.Components.Common;

namespace MyBya.Ui.Views.Components;

public class LabelEntry : BaseComponent
{
    public CEntry? Entry { get; set;}
    public event EventHandler<TextChangedEventArgs> TextChanged = delegate { };

    public static readonly BindableProperty BorderColorProperty = BindableProperty.Create(
        nameof(BorderColor),
        typeof(Color),
        typeof(LabelEntry),
        default,
        BindingMode.TwoWay,
		propertyChanged: (bindableObject, oldValue, newValue) =>
        {
            var ctrl = (LabelEntry)bindableObject;

            if (ctrl.Entry == null)
                return;

            ctrl.Entry.BorderColor = (Color)newValue;
        }
    );

    public static readonly BindableProperty EntryTextProperty = BindableProperty.Create(
        nameof(EntryText),
        typeof(string),
        typeof(BaseComponent),
        default,
        BindingMode.TwoWay,
        propertyChanged: (bindableObject, oldValue, newValue) =>
        {
            var ctrl = (LabelEntry)bindableObject;

            if (ctrl.Entry == null)
                return;

            ctrl.Entry.Text = (string)newValue;
        }
    );

     public static readonly BindableProperty PlaceholderProperty = BindableProperty.Create(
        nameof(Placeholder),
        typeof(string),
        typeof(BaseComponent),
        default,
        BindingMode.TwoWay,
		propertyChanged: (bindableObject, oldValue, newValue) =>
		{
			var ctrl = (LabelEntry)bindableObject;

            if (ctrl.Entry == null)
                return;

			ctrl.Entry.Placeholder = (string)newValue;
		}
    );

    public static readonly BindableProperty EntryHorizontalOptionsProperty = BindableProperty.Create(
        nameof(EntryHorizontalOptions),
        typeof(LayoutOptions),
        typeof(LabelEntry),
        LayoutOptions.Start,
        BindingMode.TwoWay,
        propertyChanged: (bindableObject, oldValue, newValue) =>
        {
            var ctrl = (LabelEntry)bindableObject;

            if (ctrl.Entry == null)
                return;

            ctrl.Entry.HorizontalOptions = (LayoutOptions)newValue;
        }
    );

    public static readonly BindableProperty EntryHeightRequestProperty = BindableProperty.Create(
        nameof(EntryHeightRequest),
        typeof(double),
        typeof(LabelEntry),
        0.0,
        BindingMode.TwoWay,
        propertyChanged: (bindableObject, oldValue, newValue) =>
        {
            var ctrl = (LabelEntry)bindableObject;

            if (ctrl.Entry == null)
                return;

            ctrl.Entry.HeightRequest = (double)newValue;
        }
    );

    public static readonly BindableProperty EntryWidthRequestProperty = BindableProperty.Create(
        nameof(EntryWidthRequest),
        typeof(double),
        typeof(LabelEntry),
        0.0,
        BindingMode.TwoWay,
        propertyChanged: (bindableObject, oldValue, newValue) =>
        {
            var ctrl = (LabelEntry)bindableObject;

            if (ctrl.Entry == null)
                return;

            ctrl.Entry.WidthRequest = (double)newValue;
        }
    );

    public static readonly BindableProperty EntryIsPasswordProperty = BindableProperty.Create(
        nameof(EntryIsPassword),
        typeof(bool),
        typeof(LabelEntry),
        false,
        BindingMode.TwoWay,
        propertyChanged: (bindableObject, oldValue, newValue) =>
        {
            var ctrl = (LabelEntry)bindableObject;
            if (ctrl.Entry == null)
                return;
            ctrl.Entry.IsPassword = (bool)newValue;
        }
    );

    public Color BorderColor
    {
        get => (Color)GetValue(BorderColorProperty);
        set => SetValue(BorderColorProperty, value);
    }

    public string EntryText
    {
        get => (string)GetValue(EntryTextProperty);
        set => SetValue(EntryTextProperty, value);
    }

    public string Placeholder
	{
		get => (string)GetValue(PlaceholderProperty);
		set => SetValue(PlaceholderProperty, value);
	}

    public LayoutOptions EntryHorizontalOptions
    {
        get => (LayoutOptions)GetValue(EntryHorizontalOptionsProperty);
        set => SetValue(EntryHorizontalOptionsProperty, value);
    }

    public double EntryHeightRequest
    {
        get => (double)GetValue(EntryHeightRequestProperty);
        set => SetValue(EntryHeightRequestProperty, value);
    }

    public double EntryWidthRequest
    {
        get => (double)GetValue(EntryWidthRequestProperty);
        set => SetValue(EntryWidthRequestProperty, value);
    }

    public bool EntryIsPassword
    {
        get => (bool)GetValue(EntryIsPasswordProperty);
        set => SetValue(EntryIsPasswordProperty, value);
    }

    private bool useLeftPadding;
    public bool UseLeftPadding
    {
        get => useLeftPadding;
        set
        {
            useLeftPadding = value;

            if (Entry != null)
            {
                Entry.UseLeftPadding = value;
            }
        }
    }

    public Keyboard Keyboard
    {
        set
        {
            if (Entry != null)
            {
                Entry.Keyboard = value;
            }
        }
    }

    protected override void BuildLayout()
    {
        Entry = new CEntry
        {
            BackgroundColor = Colors.White,
        };
        Entry.TextChanged += Entry_TextChanged;

        MainContent.Add(Entry);
    }

    private void Entry_TextChanged(object? sender, TextChangedEventArgs e)
    {
        EntryText = e.NewTextValue;
        TextChanged?.Invoke(Entry, new TextChangedEventArgs(e.OldTextValue, e.NewTextValue));
    }
}
