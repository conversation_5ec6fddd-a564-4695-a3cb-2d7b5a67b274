using System;
using System.Collections.ObjectModel;
using System.Runtime.CompilerServices;
using Microsoft.IdentityModel.Abstractions;
using MyBya.Models.Common;

namespace MyBya.Models;

public class WorkoutsModel : BaseModel
{
    public string WorkoutName { get; set; }
    public string WorkoutDescription { get; set; }
    public string WorkoutComment { get; set; }
    public List<WorkoutLabel> WorkoutLabels { get; set; }

    public WorkoutsModel()
    {
        WorkoutLabels = [];
    }
}

public class WorkoutLabel
{
    public string Text { get; set; }
}

public class Comments
{
    public string WorkoutComment { get; set; }
}
