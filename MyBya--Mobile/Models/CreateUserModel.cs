using MyBya.Models.Common;
using System;

namespace MyBya.Models;

public class CreateUserModel : BaseModel
{
    // base Id is hidden because Abp endpoint expects a string Id
    public new string Id { get; set; }
    public string UserName { get; set; }
    public string Name { get; set; }
    public string Surname { get; set; }
    public string Email { get; set; }
    public string PhoneNumber { get; set; }
    public bool IsActive { get; set; }
    public bool LockoutEnabled { get; set; }
    public DateTime CreationTime { get; set; }
}
