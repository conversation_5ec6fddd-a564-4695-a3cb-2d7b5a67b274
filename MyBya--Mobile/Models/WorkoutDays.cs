using MyBya.Models.Common;
namespace MyBya.Models;

public class WorkoutDays : BaseModel
{
    public DateTime Date { get; set; }
    public List<Workouts> Workouts { get; set; } = new();

    private Color cardBackgroundColor;
    public Color CardBackgroundColor
    {
        get => cardBackgroundColor;
        set => SetProperty(ref cardBackgroundColor, value);
    }

    private bool isSelected;
    public bool IsSelected
    {
        get => isSelected;
        set => SetProperty(ref isSelected, value);
    }
}
