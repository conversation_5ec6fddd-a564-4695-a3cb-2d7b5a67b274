using System;

namespace MyBya.Models;

public class BepsTestResultModel : BepsTestDataModel
{
    private Color boxColor = Colors.Transparent;
    public Color BoxColor
    {
        get => boxColor;
        set => SetProperty(ref boxColor, value);
    }

    public int Position { get; set; }
    public bool isDeleteButtonVisible;
    public bool IsDeleteButtonVisible
    {
        get => isDeleteButtonVisible;
        set => SetProperty(ref isDeleteButtonVisible, value);
    }

    public void SetColor(int index)
    {
        BoxColor = index switch
        {
            1 => Color.FromArgb("#FF7FE8"),
            2 => Color.FromArgb("#231B3B"),
            3 => Color.FromArgb("#72FFFF"),
            4 => Color.FromArgb("#6F658C"),
            _ => Colors.Transparent,
        };
    }
}
