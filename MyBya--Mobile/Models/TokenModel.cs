using System;
using System.Text.Json.Serialization;
using MyBya.Models.Common;

namespace MyBya.Models;

public class TokenModel : BaseModel
{
    [JsonPropertyName("access_token")]
    public string AccessToken { get; set; }

    [JsonPropertyName("token_type")]
    public string TokenType { get; set; }

    [JsonPropertyName("expires_in")]
    public int ExpiresIn { get; set; }

    [JsonPropertyName("refresh_token")]
    public string RefreshToken { get; set; }

    [JsonPropertyName("user_id")]
    public int UserId { get; set; }

    [JsonPropertyName("member_id")]
    public int MemberId { get; set; }

    [JsonPropertyName("abp_user_id")]
    public string AbpUserId { get; set; }
}
