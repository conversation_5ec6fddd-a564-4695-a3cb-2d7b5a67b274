using System;
using Android.Graphics.Drawables.Shapes;
using Microsoft.Maui.Handlers;
using MyBya.Ui.Views.Components;
using Android.Graphics.Drawables;
using ShapeDrawable = Android.Graphics.Drawables.ShapeDrawable;
using Color = Android.Graphics.Color;
using Paint = Android.Graphics.Paint;

namespace MyBya.Platforms.Android.Handlers;

public class EntryHandlerDroid
{
    public static void Init()
    {
        EntryHandler.Mapper.AppendToMapping(nameof(IView.Background), (handler, view) =>
        {
            if (view is CEntry cEntry)
            {
                float[] outerRadii = { 10, 10, 10, 10, 10, 10, 10, 10 };
                RoundRectShape roundRectShape = new RoundRectShape(outerRadii, null, null);

                var shape = new ShapeDrawable(roundRectShape);
                shape.Paint.Color = Color.Black;
                shape.Paint.StrokeWidth = 3;
                shape.Paint.SetStyle(Paint.Style.Stroke);

                var backgroundShape = new ShapeDrawable(roundRectShape);
                backgroundShape.Paint.Color = Color.White;
                backgroundShape.Paint.SetStyle(Paint.Style.Fill);

                var layers = new Drawable[] { backgroundShape, shape };
                var layerDrawable = new LayerDrawable(layers);

                handler.PlatformView.Background = layerDrawable;

                // Add left padding to move text away from border
                var density = handler.PlatformView.Context.Resources.DisplayMetrics.Density;
                var paddingLeft = (int)(10 * density);
                handler.PlatformView.SetPadding(paddingLeft, handler.PlatformView.PaddingTop, handler.PlatformView.PaddingRight, handler.PlatformView.PaddingBottom);
            }
        });
    }
}
