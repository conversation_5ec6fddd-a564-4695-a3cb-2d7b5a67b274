using AutoMapper;
using MyBya.Entities;
using MyBya.Models;
using MyBya.Services.DTOs;

namespace MyBya.AutoMapper;

public class ConfigurationMapper : Profile
{
    public ConfigurationMapper()
    {
        CreateMap<AthleteModel, AthleteDto>();
        CreateMap<AppUserModel, AppUserDto>();
        CreateMap<CreateUserDto, CreateUserModel>();
        CreateMap<AthleteTestSetupResponseDto, AthleteTestSetupModel>();
        CreateMap<BepsTestDataResponseDto, BepsTestDataModel>();
        CreateMap<MemberServiceDto, MemberModel>();
        CreateMap<TestResultHeartRateModel, TestResultHeartRatesEntity>();
        CreateMap<TestResultPaceChartEntity, TestResultPaceChartModel>();
        CreateMap<TestResultPaceChartModel, TestResultPaceChartEntity>();
        CreateMap<TestCalendarDto, TestCalendarModel>();
        CreateMap<TestDataEntryDto, TestDataEntryModel>();
        CreateMap<TestResultAnrcDto, TestResultAnrcModel>();
        CreateMap<TrainingPlanDetailDto, TrainingPlanDetailModel>();
        CreateMap<TrainingPlanEventDto, TrainingPlanEventModel>();
        CreateMap<TestProtocolInstructionDto, TestProtocolInstructionModel>();
        CreateMap<TrainingPlanTemplateDto, TrainingPlanTemplateModel>();
        CreateMap<GenerateTrainingPlanDto, GenerateTrainingPlanModel>();
        CreateMap<UserTrainingPlanDto, UserTrainingPlanModel>();
        CreateMap<TerraAuthDto, TerraAuthModel>();
        CreateMap<AthleteTestSetupModel, AthleteTestSetupEntity>();
        CreateMap<TestResultHeartRatesEntity, TestResultHeartRateModel>();
        CreateMap<WorkoutScheduleDto, WorkoutScheduleModel>();
        CreateMap<TestDateModel, TestDateDTO>();
        CreateMap<TestDateDTO, TestDateModel>();
        CreateMap<LoginDto, LoginModel>();
        CreateMap<AbpUserDto, AbpUserModel>();
    }
}
