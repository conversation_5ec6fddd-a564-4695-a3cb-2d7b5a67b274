﻿{
 "StagingEnvironment": "https://orca-app-ze4uf.ondigitalocean.app/",
  //"StagingEnvironment": "https://localhost:44365/",
  "MemberServiceURL": "api/app/member?Sorting={0}&SkipCount={1}&MaxResultCount={2}",
  "AppUserServiceURL": "api/app/app-user/{0}",
  "AthletesServiceURL": "api/app/athlete?MaxResultCount={0}",
  "AthleteTestSetupURL": "api/app/athlete-test-setup/",
  "TestCalendarURL": "api/app/test-calendar/{0}",
  "TestDataEntryURL": "api/app/test-data-entry",
  "BepsTestDataURL": "api/app/beps-test-data-entry/beps-test-data-entry",
  "TestResultByTestDetailAthleteIdURL": "api/app/test-result/by-test-detail-athlete-id/{0}",
  "TestResultHeartRateURL": "api/app/test-result-heart-rate/by-test-detail-athlete-id/{0}",
  "TestResultPaceChartURL": "api/app/test-result-pace-chart/by-test-detail-athlete-id/{0}",
  "TestResultAnrcURL": "api/app/test-result-anrc/by-test-detail-athlete-id/{0}",
  "TrainingPlanDetailURL": "api/app/training-plan-detail/training-plan-details-with-workouts/",
  "TrainingPlanEventURL": "api/app/training-plan-event/{0}",
  "TestProtocolInstructionBySportURL": "api/app/test-protocol-instruction/by-sport?sport={0}&intervalType={1}",
  "TrainingPlanTemplateURL": "api/app/training-plan-template",
  "GenerateTrainingPlanURL": "api/app/training-plan-detail/generate-training-plan-from-template",
  "UserTrainingPlanURL": "api/app/user-training-plan/by-member-id-with-template/",
  "TerraAuthURL": "api/auth/terra-auth",
  "CurrentTestsWithCalendarURL": "api/app/athlete-test-setup/current-tests-with-calendar",
  "TestSetupByMemberAndSportURL": "api/app/athlete-test-setup/tests-by-member-and-sport",
  "LoginURL": "connect/token",
  "TestSetupMostRecentTestsByMember": "api/app/athlete-test-setup/most-recent-tests-by-member",
  "TrainingPlanCalendarWeekViewURL":"api/app/training-plan-calendar/week-view",
  "UserInfoURL": "api/identity/users/",
  "AdminUserName": "admin",
  "AdminPass": "TestPass123!",
  "DeleteUserURL": "api/app/app-user/",
  "AthleteTrainingCommentsURL": "api/app/athlete-training-comment"
}
