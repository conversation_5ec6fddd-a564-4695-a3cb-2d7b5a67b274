using MyBya.Models;
using MyBya.Services.DTOs;

namespace MyBya.Interfaces;

public interface IAuthenticationService
{
    Task<LoginModel?> LoginAsync(string username, string password, bool rememberMe = true);
    Task<bool> ForgotPasswordAsync(string email);
    Task<bool> LogoutAsync();
    Task<bool> IsUserLoggedInAsync();
    Task<string?> GetTokenAsync(string key);
    Task<string?> GetTokenExpirationValue();
    Task<CreateUserModel?> CreateUserAsync(CreateUserDto createUserDto);
    Task<AbpUserModel?> UpdateUserAsync(EditUserDto editUserDto);
    Task<bool> IsTokenValidAsync(string key);
    Task<bool> DeleteUserAsync(string userId);
}
