using System;
using MyBya.Constants;
using MyBya.Models;
using MyBya.Services.Common;
using MyBya.Services.DTOs;
using Serilog;

namespace MyBya.Services;

public class TrainingPlanCalendarService : BaseService<WorkoutScheduleModel, WorkoutScheduleDto>
{
    public async Task<WorkoutScheduleModel> GetWeekViewByMemberIdAndDate(int memberId, DateTime date)
    {
        string convertedDate = date.ToString("yyyy-MM-dd");
        apiUrl = $"{ApiKeys.GetURL("TrainingPlanCalendarWeekViewURL")}?Date={convertedDate}&MemberId={memberId}";

        try
        {
            var dto = await GetRequestWithModel();

            var model = new WorkoutScheduleModel
            {
                Days = dto?.Days ?? [],
            };

            return model;
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            return new WorkoutScheduleModel();
        }
    }
}
