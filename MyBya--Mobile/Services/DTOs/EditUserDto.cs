using System;
using MyBya.Services.Common;

namespace MyBya.Services.DTOs;

public class EditUserDto : BaseDto
{
    // base Id is hidden because Abp endpoint expects a string Id
    public new string Id { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Surname { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public bool LockoutEnabled { get; set; }
    public List<string> RoleNames { get; set; } = new List<string>();
    public Dictionary<string, object> ExtraProperties { get; set; }
}
