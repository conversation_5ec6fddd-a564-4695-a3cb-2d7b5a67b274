using System;
using MyBya.Services.Common;
using MyBya.Services.DTOs;

namespace MyBya.Services.DTOs;

public class AbpUserDto : BaseDto
{
    public new string Id { get; set; }
    public Guid? TenantId { get; set; }
    public string UserName { get; set; }
    public string Name { get; set; }
    public string Surname { get; set; }
    public string Email { get; set; }
    public bool EmailConfirmed { get; set; }
    public string PhoneNumber { get; set; }
    public bool PhoneNumberConfirmed { get; set; }
    public bool IsActive { get; set; }
    public bool LockoutEnabled { get; set; }
    public int AccessFailedCount { get; set; }
    public DateTime? LockoutEnd { get; set; }
    public string ConcurrencyStamp { get; set; }
    public int EntityVersion { get; set; }
    public DateTime LastPasswordChangeTime { get; set; }
    public bool IsDeleted { get; set; }
    public Guid? DeleterId { get; set; }
    public DateTime? DeletionTime { get; set; }
    public DateTime LastModificationTime { get; set; }
    public Guid? LastModifierId { get; set; }
    public DateTime CreationTime { get; set; }
    public Guid? CreatorId { get; set; }
    public Dictionary<string, object> ExtraProperties { get; set; }

    public AbpUserDto()
    {
        UserName = string.Empty;
        Name = string.Empty;
        Surname = string.Empty;
        Email = string.Empty;
        PhoneNumber = string.Empty;
        ConcurrencyStamp = string.Empty;
        ExtraProperties = new Dictionary<string, object>();
    }
}
