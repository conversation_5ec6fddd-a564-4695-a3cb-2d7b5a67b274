using MyBya.Interfaces;
using MyBya.Models;
using MyBya.Services.Common;
using MyBya.Services.DTOs;
using MyBya.Constants;
using Serilog;
using Newtonsoft.Json;
using System.IdentityModel.Tokens.Jwt;
using System.Diagnostics;
using MyBya.Shared;

namespace MyBya.Services;

public class AuthenticationService : BaseService<LoginModel, LoginDto>, IAuthenticationService
{
    private readonly ISecureStorage _secureStorage;

    public AuthenticationService()
    {
        _secureStorage = SecureStorage.Default;
    }

    public async Task<LoginModel?> LoginAsync(string username, string password, bool rememberMe = true)
    {
        try
        {
            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                return null;

            apiUrl = ApiKeys.GetURL("LoginURL");

            var loginDto = new LoginRequestDto
            {
                UserNameOrEmailAddress = username,
                Password = password,
            };

            var response = await LoginPostRequest(loginDto);

            if (!response.Success)
            {
                Log.Logger.Error("Login failed: {ErrorMessage}", response.ErrorMessage);
                return new LoginModel
                {
                    Success = false,
                    Description = response.ErrorMessage ?? "An unexpected error occurred"
                };
            }

            if (response.Token == null)
            {
                Log.Logger.Error("Login succeeded but no token was received");
                return new LoginModel
                {
                    Success = false,
                    Description = "Authentication failed: No token received"
                };
            }

            await StoreTokenAsync(response.Token);

            return new LoginModel
            {
                Success = true,
                Description = "Login successful"
            };
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Login failed with exception");
            return new LoginModel
            {
                Success = false,
                Description = "An unexpected error occurred. Please try again later."
            };
        }
    }

    public Task<bool> RegisterAsync(string username, string email, string password)
    {
        // TODO: Implement actual registration logic
        return Task.FromResult(true);
    }

    public async Task<bool> ForgotPasswordAsync(string email)
    {
        try
        {
            if (string.IsNullOrEmpty(email))
            {
                return false;
            }

            // TODO: Implement actual password reset API call
            Log.Logger.Information("Password reset requested");
            return true;
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Error during password reset");
            return false;
        }
    }

    public Task<bool> LogoutAsync()
    {
        try
        {
            _secureStorage.Remove(SecureStorageConstants.UserTokenKey);
            _secureStorage.Remove(SecureStorageConstants.UserRefreshTokenKey);
            _secureStorage.Remove(SecureStorageConstants.TokenExpirationKey);
            _secureStorage.Remove(SecureStorageConstants.AbpUserId);
            _secureStorage.Remove(SecureStorageConstants.UserId);
            _secureStorage.Remove(SecureStorageConstants.MemberId);
            _secureStorage.Remove(SecureStorageConstants.UserEmail);
            _secureStorage.Remove(SecureStorageConstants.AdminToken);
            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Logout failed");
            return Task.FromResult(false);
        }
    }

    public async Task<string?> GetTokenAsync(string key)
    {
        try
        {
            return await _secureStorage.GetAsync(key);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Error retrieving token");
            return null;
        }
    }

    private async Task StoreTokenAsync(string token)
    {
        try
        {
            var tokenModel = System.Text.Json.JsonSerializer.Deserialize<TokenModel>(token);

            if (tokenModel != null)
            {
                await _secureStorage.SetAsync(SecureStorageConstants.UserTokenKey, tokenModel.AccessToken);
                await _secureStorage.SetAsync(SecureStorageConstants.AbpUserId, tokenModel.AbpUserId);
                await _secureStorage.SetAsync(SecureStorageConstants.UserId, tokenModel.UserId.ToString());
                await _secureStorage.SetAsync(SecureStorageConstants.MemberId, tokenModel.MemberId.ToString());

                if (!string.IsNullOrEmpty(tokenModel.RefreshToken))
                    await _secureStorage.SetAsync(SecureStorageConstants.UserRefreshTokenKey, tokenModel.RefreshToken);

                var expirationTime = DateTime.UtcNow.AddSeconds(tokenModel.ExpiresIn);
                await _secureStorage.SetAsync(SecureStorageConstants.TokenExpirationKey, expirationTime.ToString("O"));
            }
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Error storing token");
            throw;
        }
    }

    public async Task<bool> IsUserLoggedInAsync()
    {
        try
        {
            var token = await _secureStorage.GetAsync(SecureStorageConstants.UserTokenKey);

            if (string.IsNullOrEmpty(token))
                return false;

            var expirationTimeStr = await _secureStorage.GetAsync(SecureStorageConstants.TokenExpirationKey);

            if (string.IsNullOrEmpty(expirationTimeStr))
                return false;

            if (DateTime.TryParse(expirationTimeStr, out DateTime expirationTime))
                return DateTime.Now < expirationTime;

            return false;
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Error checking token validity");
            return false;
        }
    }

    public async Task<string?> GetTokenExpirationValue()
    {
        try
        {
            return await _secureStorage.GetAsync(SecureStorageConstants.TokenExpirationKey);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Error retrieving token expiration");
            return null;
        }
    }

    public async Task<CreateUserModel?> CreateUserAsync(CreateUserDto createUserDto)
    {
        try
        {
            bool flowControl = await SaveAdminTokenAsync();

            if (!flowControl)
                return null;

            apiUrl = ApiKeys.GetURL("UserInfoURL");
            string contentJson = JsonConvert.SerializeObject(createUserDto);
            var response = await PostRequest(contentJson, useAdminToken: true);

            if (string.IsNullOrEmpty(response))
            {
                Log.Logger.Error("Create user failed: No response from server");
                return null;
            }

            var model = JsonConvert.DeserializeObject<CreateUserModel>(response);

            if (model == null)
            {
                Log.Logger.Error("Create user failed: Response deserialization returned null");
                return null;
            }

            return model;
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Error creating user");
            return null;
        }
    }

    public async Task<AbpUserModel?> UpdateUserAsync(EditUserDto editUserDto)
    {
        try
        {
            bool flowControl = await SaveAdminTokenAsync();

            if (!flowControl)
                return null;

            apiUrl = string.Concat(ApiKeys.GetURL("UserInfoURL"), editUserDto.Id);

            string contentJson = JsonConvert.SerializeObject(editUserDto);
            var response = await PutRequest(contentJson, useAdminToken: true);

            if (string.IsNullOrEmpty(response))
            {
                Log.Logger.Error("Update user failed: No response from server");
                return null;
            }

            var dto = JsonConvert.DeserializeObject<AbpUserDto>(response);
            var model = Mapper.Map<AbpUserModel>(dto);
            return model;
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Error updating user");
            return null;
        }
    }

    private async Task<bool> SaveAdminTokenAsync()
    {
        string response = await GetAdminTokenRequest();

        if (string.IsNullOrEmpty(response))
        {
            Log.Logger.Error("Create user failed: No response from server");
            return false;
        }

        var tokenModel = System.Text.Json.JsonSerializer.Deserialize<TokenModel>(response);

        if (tokenModel == null || string.IsNullOrEmpty(tokenModel.AccessToken))
        {
            Log.Logger.Error("Failed to obtain admin token: No response from server");
            return false;
        }

        await _secureStorage.SetAsync(SecureStorageConstants.AdminToken, tokenModel.AccessToken);

        return true;
    }

    public async Task<bool> IsTokenValidAsync(string key)
    {
        try
        {
            var token = await GetTokenAsync(key);

            if (string.IsNullOrEmpty(token))
                return false;

            var handler = new JwtSecurityTokenHandler();

            if (!handler.CanReadToken(token))
                return false;

            var jwtToken = handler.ReadJwtToken(token);

            // Verifica se o token já expirou
            var expirationTime = jwtToken.ValidTo;
            return DateTime.UtcNow < expirationTime;
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Error validating JWT token");
            return false;
        }
    }

    public async Task<bool> DeleteUserAsync(string userId)
    {
        try
        {
            apiUrl = string.Concat(ApiKeys.GetURL("UserInfoURL"), userId);

            var deletionDto = await DeleteRequest(userId, useAdminToken: true);

            if (!deletionDto.Success)
            {
                Log.Logger.Warning("Delete user failed: Unauthorized (admin token may be invalid or insufficient permissions).");
                return false;
            }

            return deletionDto.Success;
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Delete user failed with exception");
            return false;
        }
    }
}
