using System;
using MyBya.Constants;
using MyBya.Models;
using MyBya.Services.Common;
using MyBya.Services.DTOs;
using Newtonsoft.Json;
using Serilog;

namespace MyBya.Services;

public class AppUserService : BaseService<AppUserModel, AppUserDto>
{
    public async Task<AppUserModel?> GetUser(int id)
    {
        apiUrl = string.Format(ApiKeys.GetURL("AppUserServiceURL"), id);

        var dto = await GetRequestWithModel();
        var model = Mapper.Map<AppUserModel>(dto);
        return model;
    }

    public async Task<AbpUserModel?> GetAbpUserById(string id)
    {
        try
        {
            apiUrl = ApiKeys.GetURL("UserInfoURL") + id;

            string response = await GetRequest(useAdminToken: true);

            if (string.IsNullOrEmpty(response))
            {
                return null;
            }

            var dto = JsonConvert.DeserializeObject<AbpUserDto>(response);
            var model = Mapper.Map<AbpUserModel>(dto);
            return model;
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            return null;
        }
    }
}
