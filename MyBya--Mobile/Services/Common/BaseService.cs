using System;
using System.Diagnostics;
using System.Net;
using System.Net.Http.Headers;
using System.Text;
using AutoMapper;
using MyBya.Configuration;
using MyBya.Constants;
using MyBya.Extensions;
using MyBya.Helpers;
using MyBya.Interfaces;
using MyBya.Models;
using MyBya.Models.Api;
using MyBya.Models.Common;
using MyBya.Services.DTOs;
using Newtonsoft.Json;
using Serilog;

namespace MyBya.Services.Common;

public abstract class BaseService<T, E> where T : BaseModel where E : BaseDto
{
    private HttpClient _client { get; set; }
    protected string apiUrl { get; set; } = string.Empty;
    protected IMapper Mapper { get; set; }

    public BaseService()
    {
// #if DEBUG
//         var handlerDebug = new HttpClientHandler
//         {
//             ServerCertificateCustomValidationCallback = (message, cert, chain, sslPolicyErrors) => true
//         };

//         _client = new HttpClient(handlerDebug)
//         {
//             BaseAddress = new Uri("https://localhost:44365")
//         };
// #else
//        var handler = new HttpClientHandler
//        {
//            ServerCertificateCustomValidationCallback = (message, cert, chain, sslPolicyErrors) => true
//        };

//        _client = new HttpClient(handler);
// #endif

        _client = new HttpClient();
        Mapper = ServiceHelper.GetService<IMapper>();
    }

    protected async Task<E?> GetRequestWithModel()
    {
        try
        {
#if DEBUG
            Debug.WriteLine($" ====> API REQUEST: {apiUrl} <=====");
#endif

            var response = await _client.GetAsync(apiUrl);

            switch (response.StatusCode)
            {
                case HttpStatusCode.OK:
                    var result = await GetJsonResponseWithModel(response);
                    if (result == null)
                    {
                        throw new Exception("Received null response from GetJsonResponseWithModel.");
                    }
                    return result;
            }

            string errorMessage = await GetJsonResponse(response);
            throw new Exception($"Error on Get request, status code: {response.StatusCode}");
        }
        catch (HttpRequestException ex)
        {
            Log.Logger.Error(ex, "Server connection failed: {Message}", ex.Message);
            throw new Exception("Unable to connect to the server. Please check your internet connection and try again.", ex);
        }
        catch (TaskCanceledException ex)
        {
            Log.Logger.Error(ex, "Request timeout: {Message}", ex.Message);
            throw new Exception("Request timed out. The server may be unavailable.", ex);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            throw;
        }
    }

    protected async Task<List<E>> GetListRequestWithModel()
    {
        try
        {
#if DEBUG
            Debug.WriteLine($" ====> API REQUEST: {apiUrl} <=====");
#endif
            var response = await _client.GetAsync(apiUrl);

            switch (response.StatusCode)
            {
                case HttpStatusCode.OK:
                    return await GetJsonListResponseWithModel(response);
            }

            string errorMessage = await GetJsonResponse(response);
            throw new Exception($"Error on Get request, status code: {response.StatusCode}");
        }
        catch (HttpRequestException ex)
        {
            Log.Logger.Error(ex, "Server connection failed: {Message}", ex.Message);
            throw new Exception("Unable to connect to the server. Please check your internet connection and try again.", ex);
        }
        catch (TaskCanceledException ex)
        {
            Log.Logger.Error(ex, "Request timeout: {Message}", ex.Message);
            throw new Exception("Request timed out. The server may be unavailable.", ex);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            throw;
        }
    }

    protected async Task<string> GetRequest(bool useJwt = false, bool useAdminToken = false)
    {
        try
        {
#if DEBUG
            Debug.WriteLine($" ====> API REQUEST: {apiUrl} <=====");
#endif
            var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            request.Headers.Accept.Clear();
            request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            if (useJwt || useAdminToken)
            {
                var token = await GetTokenAsync(useAdminToken);

                if (!string.IsNullOrWhiteSpace(token))
                    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
            }

            var response = await _client.SendAsync(request);

            switch (response.StatusCode)
            {
                case HttpStatusCode.OK:
                    return await GetJsonResponse(response);
            }

            string errorMessage = await GetJsonResponse(response);
            throw new Exception($"Error on Get request, status code: {response.StatusCode}");
        }
        catch (HttpRequestException ex)
        {
            Log.Logger.Error(ex, "Server connection failed: {Message}", ex.Message);
            throw new Exception("Unable to connect to the server. Please check your internet connection and try again.", ex);
        }
        catch (TaskCanceledException ex)
        {
            Log.Logger.Error(ex, "Request timeout: {Message}", ex.Message);
            throw new Exception("Request timed out. The server may be unavailable.", ex);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            throw;
        }
    }

    protected async Task<E> PostRequestWithModel(string contentJson)
    {
        try
        {
#if DEBUG
            Debug.WriteLine($" ====> API REQUEST: {contentJson} <=====");
#endif
            StringContent content = GetStringContent(contentJson);

            HttpResponseMessage response = await _client.PostAsync(apiUrl, content);

            switch (response.StatusCode)
            {
                case HttpStatusCode.Created:
                case HttpStatusCode.OK:
                    var result = await GetJsonResponseWithModel(response);
                    if (result == null)
                    {
                        throw new Exception("Received null response from GetJsonResponseWithModel.");
                    }
                    return result;

                case HttpStatusCode.BadRequest:
                    return default(E)!;

                default:
                    var error = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"Error: {response.StatusCode} - {error}");
                    return default(E)!;
            }
        }
        catch (HttpRequestException ex)
        {
            Log.Logger.Error(ex, "Server connection failed: {Message}", ex.Message);
            throw new Exception("Unable to connect to the server. Please check your internet connection and try again.", ex);
        }
        catch (TaskCanceledException ex)
        {
            Log.Logger.Error(ex, "Request timeout: {Message}", ex.Message);
            throw new Exception("Request timed out. The server may be unavailable.", ex);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            throw;
        }
    }

    protected async Task<string> PostRequest(string contentJson,
        bool useJwt = false,
        bool useAdminToken = false)
    {
        try
        {
#if DEBUG
            Debug.WriteLine($" ====> API REQUEST: {contentJson} <=====");
#endif
            StringContent content = GetStringContent(contentJson);

            if (useJwt || useAdminToken)
            {
                _client.DefaultRequestHeaders.Clear();
                _client.DefaultRequestHeaders.Accept.Clear();
                _client.SetBearerToken(await GetTokenAsync(useAdminToken));
            }

            HttpResponseMessage response = await _client.PostAsync(apiUrl, content);

            switch (response.StatusCode)
            {
                case HttpStatusCode.Created:
                case HttpStatusCode.OK:
                    return await GetJsonResponse(response);

                case HttpStatusCode.BadRequest:
                    return "";
            }

            var error = await response.Content.ReadAsStringAsync();
            throw new Exception($"Error on post request, status code: {response.StatusCode}");
        }
        catch (HttpRequestException ex)
        {
            Log.Logger.Error(ex, "Server connection failed: {Message}", ex.Message);
            throw new Exception("Unable to connect to the server. Please check your internet connection and try again.", ex);
        }
        catch (TaskCanceledException ex)
        {
            Log.Logger.Error(ex, "Request timeout: {Message}", ex.Message);
            throw new Exception("Request timed out. The server may be unavailable.", ex);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            throw;
        }
    }

    protected async Task<LoginResponse> LoginPostRequest(LoginRequestDto loginDto)
    {
        try
        {
            var parameters = new Dictionary<string, string>
            {
                { "grant_type", "password" },
                { "username", loginDto.UserNameOrEmailAddress ?? "" },
                { "password", loginDto.Password ?? "" },
                { "client_id", "MyByaApi_App" },
                { "scope", "MyByaApi offline_access" }
            };

            _client.DefaultRequestHeaders.Accept.Clear();
            _client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

#if DEBUG
            Debug.WriteLine($" ====> API REQUEST: {JsonConvert.SerializeObject(parameters)} <=====");
#endif

            var response = await _client.PostAsync(apiUrl, new FormUrlEncodedContent(parameters));

            var loginResponse = new LoginResponse
            {
                StatusCode = response.StatusCode
            };

            switch (response.StatusCode)
            {
                case HttpStatusCode.OK:
                    loginResponse.Token = await GetJsonResponse(response);
                    loginResponse.Success = true;
                    return loginResponse;

                case HttpStatusCode.BadRequest:
                case HttpStatusCode.Unauthorized:
                    loginResponse.Success = false;
                    loginResponse.ErrorMessage = "Invalid username or password";
                    var message = await response.Content.ReadAsStringAsync();
                    return loginResponse;

                default:
                    loginResponse.Success = false;
                    loginResponse.ErrorMessage = $"Unexpected error occurred. Status code: {response.StatusCode}";
                    var error = await response.Content.ReadAsStringAsync();
                    Log.Logger.Error("Login failed with status code {StatusCode}. Error: {Error}", response.StatusCode, error);
                    return loginResponse;
            }
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Login request failed");
            return new LoginResponse
            {
                Success = false,
                ErrorMessage = "Unable to connect to the server. Please try again later.",
                Exception = ex
            };
        }
    }

    protected async Task<string> GetAdminTokenRequest()
    {
        try
        {
            var parameters = new Dictionary<string, string>
            {
                { "grant_type", "password" },
                { "username", AppSettingsManager.Settings["AdminUserName"] },
                { "password", AppSettingsManager.Settings["AdminPass"] },
                { "client_id", "MyByaApi_App" },
                { "scope", "MyByaApi" }
            };

            _client.DefaultRequestHeaders.Clear();
            _client.DefaultRequestHeaders.Accept.Clear();
            _client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            string adminTokenUrl = ApiKeys.GetURL("LoginURL");
            var response = await _client.PostAsync(adminTokenUrl, new FormUrlEncodedContent(parameters));

            switch (response.StatusCode)
            {
                case HttpStatusCode.OK:
                    return await GetJsonResponse(response);

                case HttpStatusCode.BadRequest:
                case HttpStatusCode.Unauthorized:
                    return string.Empty;

                default:
                    var error = await response.Content.ReadAsStringAsync();
                    Log.Logger.Error("Login failed with status code {StatusCode}. Error: {Error}", response.StatusCode, error);
                    return error;
            }
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Login request failed");
            return string.Empty;
        }
    }

    protected async Task<string> PutRequest(string? contentJson = null,
        bool useJwt = false,
        bool useAdminToken = false)
    {
        try
        {
#if DEBUG
            Debug.WriteLine($" ====> API REQUEST: {contentJson} <=====");
#endif
            StringContent? content = contentJson is null ? null : GetStringContent(contentJson);

            if (useJwt || useAdminToken)
            {
                _client.DefaultRequestHeaders.Clear();
                _client.DefaultRequestHeaders.Accept.Clear();
                _client.SetBearerToken(await GetTokenAsync(useAdminToken));
            }

            HttpResponseMessage response = await _client.PutAsync(apiUrl, content);

            switch (response.StatusCode)
            {
                case HttpStatusCode.OK:
                    return await GetJsonResponse(response);

                case HttpStatusCode.Unauthorized:
                    string jsonResponse = await response.Content.ReadAsStringAsync();
                    return "";
            }

            throw new Exception($"Error on Put request, status code: {response.StatusCode}");
        }
        catch (HttpRequestException ex)
        {
            Log.Logger.Error(ex, "Server connection failed: {Message}", ex.Message);
            throw new Exception("Unable to connect to the server. Please check your internet connection and try again.", ex);
        }
        catch (TaskCanceledException ex)
        {
            Log.Logger.Error(ex, "Request timeout: {Message}", ex.Message);
            throw new Exception("Request timed out. The server may be unavailable.", ex);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            throw;
        }
    }

    protected async Task<DeletionDto> DeleteRequest(string contentJson, bool useJwt = false, bool useAdminToken = false)
    {
        try
        {
#if DEBUG
            Debug.WriteLine($" ====> API REQUEST: {contentJson} <=====");
#endif
            var request = new HttpRequestMessage
            {
                Method = HttpMethod.Delete,
                RequestUri = new Uri(apiUrl),
                Content = GetStringContent(contentJson)
            };

            if (useJwt || useAdminToken)
            {
                _client.DefaultRequestHeaders.Clear();
                _client.DefaultRequestHeaders.Accept.Clear();
                _client.SetBearerToken(await GetTokenAsync(useAdminToken));
            }

            var response = await _client.SendAsync(request);

            switch (response.StatusCode)
            {
                case HttpStatusCode.OK:
                case HttpStatusCode.NoContent:
                {
                    var message = await GetJsonResponse(response);
                    return new DeletionDto
                    {
                        Success = true,
                        Message = message
                    };
                }
                case HttpStatusCode.Unauthorized:
                {
                    var message = await GetJsonResponse(response);
                    return new DeletionDto
                    {
                        Success = false,
                        Message = message
                    };
                }
            }

            throw new Exception($"Error on Delete request, status code: {response.StatusCode}");
        }
        catch (HttpRequestException ex)
        {
            Log.Logger.Error(ex, "Server connection failed: {Message}", ex.Message);
            throw new Exception("Unable to connect to the server. Please check your internet connection and try again.", ex);
        }
        catch (TaskCanceledException ex)
        {
            Log.Logger.Error(ex, "Request timeout: {Message}", ex.Message);
            throw new Exception("Request timed out. The server may be unavailable.", ex);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            throw;
        }
    }

    protected async Task<string> UploadFile(byte[] content, string fileName)
    {
        using var formData = new MultipartFormDataContent();
        var fileStream = new MemoryStream(content);
        var fileContent = new StreamContent(fileStream);
        fileContent.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
        formData.Add(fileContent, "file", fileName);

        var response = await _client.PostAsync(apiUrl, formData);

        switch (response.StatusCode)
        {
            case HttpStatusCode.Created:
            case HttpStatusCode.OK:
                return await GetJsonResponse(response);

            case HttpStatusCode.Unauthorized:
                using (var newFormData = new MultipartFormDataContent())
                {
                    var newFileStream = new MemoryStream(content);
                    var newFileContent = new StreamContent(newFileStream);
                    newFileContent.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
                    newFormData.Add(newFileContent, "file", fileName);
                    response = await _client.PostAsync(apiUrl, newFormData);
                }
                return await GetJsonResponse(response);
        }

        string errorMessage = await GetJsonResponse(response);
        throw new Exception($"Error on uploading image, error: {errorMessage}, status code: {response.StatusCode}");
    }

    protected StringContent GetStringContent(string content)
    {
        return new StringContent(
            content: content,
            encoding: Encoding.UTF8,
            mediaType: "application/json"
        );
    }

    protected virtual string SerializeEntity<EE>(EE instance)
    {
        return JsonConvert.SerializeObject(instance);
    }

    protected virtual E GetEntity(T model)
    {
        throw new NotSupportedException("Derived classes must implement GetEntity");
    }

    private async Task<string> GetJsonResponse(HttpResponseMessage response)
    {
        string jsonResponse = await response.Content.ReadAsStringAsync();
#if DEBUG
        Debug.WriteLine($" ====> API RESPONSE: {jsonResponse} <=====");
#endif
        return jsonResponse;
    }

    private async Task<E?> GetJsonResponseWithModel(HttpResponseMessage response)
    {
        string jsonResponse = await GetJsonResponse(response);

        if (!string.IsNullOrEmpty(jsonResponse))
        {
            E? model = JsonConvert
                .DeserializeObject<E>(jsonResponse);

            return model!;
        }

        return default(E);
    }

    private async Task<List<E>> GetJsonListResponseWithModel(HttpResponseMessage response)
    {
        string jsonResponse = await GetJsonResponse(response);

        if (!string.IsNullOrEmpty(jsonResponse))
        {
            List<E> model = JsonConvert
                .DeserializeObject<List<E>>(jsonResponse) ?? new List<E>();

            return model!;
        }

        return new List<E>();
    }

    protected virtual List<EE> GetApiResponseList<EE>(string json)
        where EE : new()
    {
        ApiResponseList<EE>? apiResponse = JsonConvert
            .DeserializeObject<ApiResponseList<EE>>(json);

        return apiResponse?.Items ?? new List<EE>();
    }

    private HttpRequestMessage RefreshDeleteRequest(string contentJson)
    {
        var refreshRequest = new HttpRequestMessage();
        refreshRequest.Method = HttpMethod.Delete;
        refreshRequest.RequestUri = new Uri(apiUrl);
        refreshRequest.Content = GetStringContent(contentJson);
        return refreshRequest;
    }

    private async Task<string> GetTokenAsync(bool useAdminToken = false)
    {
        string? token = "";

        if (useAdminToken)
        {
            token = await GetAdminToken();
        }
        else
        {
            token = await GetUserTokenAsync();
        }

        string? expirationToken = await ServiceHelper
            .GetService<IAuthenticationService>()
            .GetTokenExpirationValue();

#if DEBUG
        Debug.WriteLine($" ====> TOKEN: {token} <=====");
        Debug.WriteLine($" ====> EXPIRATION: {expirationToken} <=====");
#endif

        return token ?? "";
    }

    private async Task<string> GetAdminToken()
    {
        string? token = await SecureStorage.Default
            .GetAsync(SecureStorageConstants.AdminToken);

        bool isValid = await ServiceHelper
            .GetService<IAuthenticationService>()
            .IsTokenValidAsync(SecureStorageConstants.AdminToken);

        if (!string.IsNullOrEmpty(token) && isValid)
            return token;

        // If no token is found, request a new one
        token = await GetAdminTokenRequest();

        if (string.IsNullOrEmpty(token))
        {
            Log.Logger.Error("Failed to retrieve admin token.");
            throw new Exception("Failed to retrieve admin token.");
        }

        var tokenModel = System.Text.Json.JsonSerializer.Deserialize<TokenModel>(token);

        if (tokenModel == null || string.IsNullOrEmpty(tokenModel.AccessToken))
        {
            Log.Logger.Error("Failed to deserialize admin token.");
            throw new Exception("Failed to retrieve admin token.");
        }

        await SecureStorage.Default.SetAsync(SecureStorageConstants.AdminToken, tokenModel.AccessToken);
        return tokenModel.AccessToken;
    }

    private async Task<string> GetUserTokenAsync()
    {
        bool isValid = await ServiceHelper
            .GetService<IAuthenticationService>()
            .IsTokenValidAsync(SecureStorageConstants.UserTokenKey);

        string? token;

        if (isValid)
        {
            token = await SecureStorage.Default
                .GetAsync(SecureStorageConstants.UserTokenKey);
        }
        else
        {
            token = await SecureStorage.Default
                .GetAsync(SecureStorageConstants.UserRefreshTokenKey);
        }

        return token ?? string.Empty;
    }
}
