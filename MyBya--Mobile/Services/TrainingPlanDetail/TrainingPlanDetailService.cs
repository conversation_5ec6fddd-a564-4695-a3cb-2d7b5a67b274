﻿using MyBya.Constants;
using MyBya.Models;
using MyBya.Services.Common;
using MyBya.Services.DTOs;
using MyBya.Shared;
using Serilog;

namespace MyBya.Services
{
    public class TrainingPlanDetailService : BaseService<TrainingPlanDetailModel, TrainingPlanDetailDto>
    {
        public TrainingPlanDetailService()
        {
            apiUrl = ApiKeys.GetURL("TrainingPlanDetailURL");
        }

        public async Task<List<TrainingPlanDetailModel>> GetTrainingPlansByMemberIdAndDate(int memberId, string date)
        {
            try
            {
                var requestedUrl = apiUrl + memberId + "?date=" + date;
                apiUrl = requestedUrl;

                var dtos = await GetListRequestWithModel();
                var models = new List<TrainingPlanDetailModel>();

                foreach (var dto in dtos)
                {
                    var model = Mapper.Map<TrainingPlanDetailModel>(dto);
                    models.Add(model);
                }

                return models;
            }
            catch (Exception ex)
            {
                Log.Logger.Error(ex, ex.Message);
                return new List<TrainingPlanDetailModel>();
            }
        }
    }
}
