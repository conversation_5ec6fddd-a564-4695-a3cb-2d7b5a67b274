using System;
using MyBya.Constants;
using MyBya.Models;
using MyBya.Models.Api;
using MyBya.Services.Common;
using MyBya.Services.DTOs;
using MyBya.Shared;
using Newtonsoft.Json;
using Serilog;

namespace MyBya.Services;

public class AthleteTrainingCommentService : BaseService<AthleteTrainingCommentModel, AthleteTrainingCommentDto>
{
    public async Task<bool> SaveUserComment(string comment, int trainingPlanId, DateTime date)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(comment))
            {
                Log.Logger.Warning("Attempted to save empty comment");
                return false;
            }

            if (trainingPlanId <= 0)
            {
                Log.Logger.Warning($"Invalid training plan ID: {trainingPlanId}");
                return false;
            }

            apiUrl = ApiKeys.GetURL("AthleteTrainingCommentsURL");

            var memberId = MyByaContext.Instance.GetMemberId();

            var commentDto = new AthleteTrainingCommentDto
            {
                MemberId = memberId,
                TrainingPlanId = trainingPlanId,
                Comment = comment,
                Date = date
            };

            string content = JsonConvert.SerializeObject(commentDto);
            var dto = await PostRequestWithModel(content);
            return dto != null;
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            return false;
        }
    }

    public async Task<bool> EditUserComment(string comment, int trainingPlanId, DateTime date)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(comment))
            {
                Log.Logger.Warning("Attempted to save empty comment");
                return false;
            }

            if (trainingPlanId <= 0)
            {
                Log.Logger.Warning($"Invalid training plan ID: {trainingPlanId}");
                return false;
            }

            apiUrl = ApiKeys.GetURL("AthleteTrainingCommentsURL");

            var memberId = MyByaContext.Instance.GetMemberId();

            var commentDto = new AthleteTrainingCommentDto
            {
                MemberId = memberId,
                TrainingPlanId = trainingPlanId,
                Comment = comment,
                Date = date
            };

            string content = JsonConvert.SerializeObject(commentDto);
            var response = await PutRequest();
            var dto = JsonConvert.DeserializeObject<AthleteTrainingCommentDto>(response);
            return dto != null;
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            return false;
        }
    }

    public async Task<List<AthleteTrainingCommentModel>> GetCommentsByDate(int memberId, string date, int trainingPlanId)
    {
        try
        {
            apiUrl = $"{ApiKeys.GetURL("AthleteTrainingCommentsURL")}?MemberId={memberId}&Date={date}&TrainingPlanId={trainingPlanId}";

            string json = await GetRequest();
            var dtos = JsonConvert.DeserializeObject<ApiResponseList<AthleteTrainingCommentDto>>(json);

            if (dtos == null || dtos.Items.Count == 0)
            {
                return [];
            }

            var models = new List<AthleteTrainingCommentModel>();

            foreach (var dto in dtos.Items)
            {
                var model = new AthleteTrainingCommentModel
                {
                    Id = dto.Id,
                    MemberId = dto.MemberId,
                    TrainingPlanId = dto.TrainingPlanId,
                    Comment = dto.Comment,
                    Date = dto.Date
                };

                models.Add(model);
            }

            return models;
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ex.Message);
            return [];
        }
    }
}
