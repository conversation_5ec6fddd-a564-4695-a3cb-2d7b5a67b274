using System;
using System.Globalization;

namespace MyBya.Converters;

public class TextToBoolConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is string strValue)
        {
            // Convert non-empty strings to true, empty strings to false
            return !string.IsNullOrEmpty(strValue);
        }

        // If the value is not a string, return false
        return false;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
