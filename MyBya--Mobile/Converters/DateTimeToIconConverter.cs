using System;
using System.Globalization;

namespace MyBya.Converters;

public class DateTimeToIconConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is DateTime datetime)
        {
            return datetime.Date < DateTime.Now.Date ? "ic_checkmark.png" : "ic_purple_dot.png";
        }

        return "ic_purple_dot.png";
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
