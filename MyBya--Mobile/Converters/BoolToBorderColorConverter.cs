using System;
using System.Globalization;

namespace MyBya.Converters;

public class BoolToBorderColorConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is bool isSelected)
        {
            return isSelected ? Color.FromArgb("#8051FF") : Colors.Transparent;
        }

        return Colors.Transparent; // Default color if value is not a boolean
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        return value is Color color && color == Color.FromArgb("#8051FF");
    }
}
