using System;
using AutoMapper;
using MyBya.DataAccess.Interfaces;
using MyBya.Entities;
using MyBya.Helpers;
using MyBya.Models;
using MyBya.Repository.Interface;
using MyBya.Services;
using Serilog;

namespace MyBya.Shared;

public class PaceChartProcessingShared
{
    public static async Task BuildPaceCharts(AthleteTestSetupEntity
        athleteTestSetupEntity)
    {
        List<TestResultPaceChartModel> paceCharts = await ServiceHelper
            .GetService<TestResultPaceChartService>()
            .GetPaceCharts(athleteTestSetupEntity.TestDetailAthleteId ?? 0);

        if (paceCharts is null || !paceCharts.Any())
            return;

        var paceChartsDtoList = new List<TestResultPaceChartModel>();

        TestResultPaceChartModel? newPaceChart = null;

        foreach (TestResultPaceChartModel item in paceCharts)
        {
            string currentSystem = item.System != null ? item.System.Split('-')[0] : string.Empty;

            if (item.System != null && item.System.Contains("Max"))
            {
                newPaceChart = new TestResultPaceChartModel();
                newPaceChart.System = item.System.Split('-')[0];
                newPaceChart.Arc1Max = item.Arc1;
                newPaceChart.Arc2Max = item.Arc2;
                newPaceChart.Arc3Max = item.Arc3;
                newPaceChart.LtccMax = item.Ltcc;
                continue;
            }

            if (item.System != null && item.System.Contains("Min"))
            {
                if (newPaceChart == null)
                {
                    newPaceChart = new TestResultPaceChartModel();
                    newPaceChart.System = item.System.Split('-')[0];
                }
                newPaceChart.Arc1Min = item.Arc1;
                newPaceChart.Arc2Min = item.Arc2;
                newPaceChart.Arc3Min = item.Arc3;
                newPaceChart.LtccMin = item.Ltcc;
                paceChartsDtoList.Add(newPaceChart);
                newPaceChart = null;
            }
        }

        await SetPaceChartsLocally(paceChartsDtoList, athleteTestSetupEntity);
    }

    public static async Task SetPaceChartsLocally(List<TestResultPaceChartModel> paceCharts,
         AthleteTestSetupEntity athleteTestSetup)
    {
        try
        {
            var testResultPaceChartDataAccess = ServiceHelper.GetService<ITestResultPaceChartRepository>();
            await testResultPaceChartDataAccess.Clear();

            var entities = new List<TestResultPaceChartEntity>();

            foreach (var model in paceCharts)
            {
                var entity = ServiceHelper.GetService<IMapper>()
                    .Map<TestResultPaceChartEntity>(model);

                entity.AthleteTestSetupId = athleteTestSetup.Id;
                entity.Sport = athleteTestSetup.Sport ?? 0;
                entities.Add(entity);
            }

            await testResultPaceChartDataAccess.InsertAll(entities);
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Error setting pace charts locally.");
        }
    }
}
