using MyBya.Constants;
using MyBya.Models;

namespace MyBya.Shared;

public sealed class MyByaContext
{
    private static readonly Lazy<MyByaContext> _instance = new(() => new MyByaContext());
    public static MyByaContext Instance => _instance.Value;

    private int _testDetailAthleteId { get; set; }
    private int _memberId { get; set; }
    private int _userId { get; set; }
    public AbpUserModel? CurrentUser { get; set; }
    public TestCalendarModel? CurrentTestCalendar { get; set; }
    public AthleteTestSetupModel? CurrentTestSetup { get; set; }

    public void SetCurrentTestCalendar(TestCalendarModel testCalendar)
    {
        CurrentTestCalendar = testCalendar;
    }

    public void SetCurrentTestSetup(AthleteTestSetupModel testSetup)
    {
        CurrentTestSetup = testSetup;

        if (testSetup.TestDetailAthleteId.HasValue)
        {
            SetTestDetailAthleteId(testSetup.TestDetailAthleteId.Value);
        }
        else
        {
            throw new InvalidOperationException("TestDetailAthleteId is null.");
        }
    }

    public void SetCurrentUser(AbpUserModel user)
    {
        CurrentUser = user;
    }

    public async Task SetMemberId()
    {
        string? id = await SecureStorage.Default
            .GetAsync(SecureStorageConstants.MemberId);

        if (string.IsNullOrEmpty(id))
            throw new InvalidOperationException("MemberId not found in secure storage.");

        if (int.TryParse(id, out int parsedId))
            _memberId = parsedId;
        else
            throw new FormatException("MemberId is not a valid integer.");
    }

    public async Task SetUserId()
    {
        string userId = await SecureStorage.Default
           .GetAsync(SecureStorageConstants.UserId) ?? string.Empty;

        if (string.IsNullOrEmpty(userId))
            throw new InvalidOperationException("UserId not found in secure storage.");

        if (int.TryParse(userId, out int parsedUserId))
            _userId = parsedUserId;
        else
            throw new FormatException("UserId is not a valid integer.");
    }

    public void SetTestDetailAthleteId(int id)
    {
        _testDetailAthleteId = id;
    }

    public int GetTestDetailAthleteId()
    {
        return _testDetailAthleteId;
    }

    public int GetMemberId()
    {
        return _memberId;
    }

    public int GetUserId()
    {
        return _userId;
    }

    public string GetUserName()
    {
        if (CurrentUser == null)
            return string.Empty;

        var name = CurrentUser.Name ?? string.Empty;
        var surname = CurrentUser.Surname ?? string.Empty;

        return string.IsNullOrEmpty(surname)
            ? name
            : $"{name} {surname}";
    }

    public string GetUserEmail()
    {
        return CurrentUser?.Email ?? string.Empty;
    }
}
