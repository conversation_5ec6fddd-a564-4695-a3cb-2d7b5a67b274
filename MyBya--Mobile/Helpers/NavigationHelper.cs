using System;

namespace MyBya.Helpers;

public static class NavigationHelper
{
    public static Page? GetCurrentPage()
    {
        var mainPage = Application.Current?.MainPage;
        if (mainPage == null) return null;

        // Handle TabbedPage
        if (mainPage is TabbedPage tabbedPage)
        {
            var currentTab = tabbedPage.CurrentPage;

            // If the current tab is a NavigationPage, get its current page
            if (currentTab is NavigationPage navPage)
            {
                return navPage.Navigation.NavigationStack.LastOrDefault();
            }

            // If the current tab is not a NavigationPage, return the tab itself
            return currentTab;
        }

        // Handle regular NavigationPage
        if (mainPage is NavigationPage navigationPage)
        {
            return navigationPage.Navigation.NavigationStack.LastOrDefault();
        }

        // Handle other cases (ContentPage, etc.)
        return mainPage;
    }

    public static TabbedPage? GetCurrentTabbedPage()
    {
        var mainPage = GetCurrentPage();

        if (mainPage == null)
            return null;

        if (mainPage is TabbedPage tabbedPage)
            return tabbedPage;

        return null;
    }
}


