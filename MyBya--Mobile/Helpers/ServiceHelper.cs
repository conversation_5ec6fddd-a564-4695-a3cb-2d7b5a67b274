using System;

namespace MyBya.Helpers;

public static class ServiceHelper
{
    public static T GetService<T>()
    {
        if (Current == null)
        {
            throw new InvalidOperationException("Service provider is not initialized.");
        }

        var service = Current.GetService<T>();

        if (service == null)
            throw new InvalidOperationException($"Service of type {typeof(T)} is not registered.");

        return service;
    }

    private static IServiceProvider GetAndroidServiceProvider()
    {
        IPlatformApplication? app = IPlatformApplication.Current;
        if (app == null)
            throw new InvalidOperationException("Cannot resolve current application. Services should be accessed after MauiProgram initialization.");
        return app.Services;
    }

    public static IServiceProvider Current =>

#if WINDOWS10_0_17763_0_OR_GREATER
    MauiWinUIApplication.Current.Services;
#elif ANDROID
    GetAndroidServiceProvider();
#elif IOS || MACCATALYST
    MauiUIApplicationDelegate.Current.Services;
#else
    null;
#endif
}
