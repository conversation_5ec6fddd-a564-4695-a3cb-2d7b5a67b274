using MyBya.DbContext.Handler.Interface;
using MyBya.Helpers;
using MyBya.Interfaces;
using MyBya.Ui.Pages;
using MyBya.Ui.Pages.TestProtocol;
using MyBya.Ui.ViewModels;
using MyBya.Ui.ViewModels.TestProtocol;
using Serilog;

namespace MyBya;

public partial class App : Application
{
    public App()
    {
        InitializeComponent();
        InitLogging();
        UserAppTheme = AppTheme.Light;
        MainPage = new SplashPage();
    }

    private static void InitLogging()
    {
        ServiceHelper.GetService<ILogService>().Initialize();
    }
}

